import React from 'react';

import { QueryClientProvider } from '@tanstack/react-query';
import { queryClient as ownQueryClient } from '../provider/ReactQueryProvider';
import SipCard from '../pages/SipCard';
import DeviceInfoProvider from '../provider/DeviceInfoProvider';
import AxiosProviderWrapper from '../contexts/AxiosProviderWrapper';
import NativeBackPressContext from '../contexts/NativeBackPressContext';
import { useNativeBackPress } from '../hooks/useNativeBackPress';
import SnackbarProvider from '../contexts/SnackbarProvider';

const SipCardWrapper = (props) => {
  const BackPress = useNativeBackPress();

  const { bridgeData, ...rest } = props;
  Object.entries(bridgeData).forEach(([key, val]) => {
    DeviceInfoProvider.setInfo(key, val);
  });

  return (
    <QueryClientProvider client={ownQueryClient}>
      <AxiosProviderWrapper>
        <SnackbarProvider>
          <NativeBackPressContext.Provider value={BackPress}>
            <SipCard {...rest} />
          </NativeBackPressContext.Provider>
        </SnackbarProvider>
      </AxiosProviderWrapper>
    </QueryClientProvider>
  );
};

export default SipCardWrapper;
