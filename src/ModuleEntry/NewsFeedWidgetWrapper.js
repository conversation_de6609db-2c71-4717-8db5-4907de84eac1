import { QueryClientProvider } from '@tanstack/react-query';
import { queryClient as ownQueryClient } from '../provider/ReactQueryProvider';
import { NewsFeedWidgetMemo } from '../pages/NewsFeedWidgetPage';
import DeviceInfoProvider from '../provider/DeviceInfoProvider';
import AxiosProviderWrapper from '../contexts/AxiosProviderWrapper';
import NativeBackPressContext from '../contexts/NativeBackPressContext';
import { useNativeBackPress } from '../hooks/useNativeBackPress';
import AppContextProvider from '../contexts/AppContextProvider';
import NativeDocumentHideContext from '../contexts/NativeDocumentHideContext';
import { withErrorBoundary } from '../HOC/WidgetErrorBoundary';

const NewsFeedWidgetWrapper = (props) => {
  console.log('NewsFeedWidgetWrapper :: props', props);
  const BackPress = useNativeBackPress();

  const { bridgeData, dataFeed, ...rest } = props;
  if (true) {
    Object.entries({
      Authorization: undefined,
      H5NativeDeeplinkData: '',
      appVersionName: '10.57.0',
      client_type: 'H5',
      deviceId: undefined,
      deviceName: 'x86_64 (iOS 16.2)',
      device_type: 'ios',
      h5Version: '10.57.0',
      isIos: undefined,
      isLogin: true,
      loggedInType: 'deviceBinded',
      origin: 'PAYTM',
      sso_token: 'ff24799a-bf1f-4c98-ae3b-795a09013500',
      twoFAToken: undefined,
      userId: '231478903',
    }).forEach(([key, val]) => {
      DeviceInfoProvider.setInfo(key, val);
    });
  }

  return (
    <QueryClientProvider client={ownQueryClient}>
      <AxiosProviderWrapper>
        <NativeDocumentHideContext>
          <AppContextProvider
            isFeedRequired={false} // Adjust if data feed is needed
            externalDataFeed={dataFeed}
          >
            <NativeBackPressContext.Provider value={BackPress}>
              <NewsFeedWidgetMemo {...rest} />
            </NativeBackPressContext.Provider>
          </AppContextProvider>
        </NativeDocumentHideContext>
      </AxiosProviderWrapper>
    </QueryClientProvider>
  );
};

export default withErrorBoundary(NewsFeedWidgetWrapper);
