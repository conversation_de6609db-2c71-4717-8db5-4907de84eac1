// Light mode variables
@mixin lightVars {
  // Brand
  --brandPrimary: #1576db;
  --brandSecondary: #00b8f5;

  // Background
  --plain: #ffffff;
  --searchBG: #f1f5fa;
  --plainVariant: #fafafa;
  --popup: #fefefe;
  --toast: #dadada;
  --glass: rgba(255, 255, 255, 0.6);
  --surfaceLevel1: #ffffff;
  --surfaceLevel2: #fdfdfd;
  --main1: linear-gradient(to bottom, #eef5ff, #ffffff);
  --main2: linear-gradient(to bottom, #d0e5ff, #e9f7ff);
  // Colors
  --primaryOffset: #ecf2f8;
  --primaryOffsetVariant: #d8e7f7;
  --secondaryOffset: #f5f0f0;
  --secondaryOffsetVariant: #e8e1e1;
  --primary: #101010;
  --neutral: #282828;
  --neutralModerate: #48494c;
  --overlay: rgba(16, 16, 16, 0.7);
  --overlayLight: rgba(16, 16, 16, 0.13);
  --linksSelections: #1576db;
  --secondary: #00b8f5;
  --secondaryLight: #e0f5fd;
  --positive: #52cb77;
  --positiveMedium: #e3f6ec;
  --positiveLight: #f1fbf5;
  --negative: #e63757;
  --negativeMedium: #ffebef;
  --negativeLight: #fff5f7;
  --warning: #f8a60a;
  --warningMedium: #fff8e1;
  --warningLight: #fffbf0;

  // Border
  --grey900: #101010;
  --grey500: rgba(16, 16, 16, 0.54);
  --grey200: rgba(16, 16, 16, 0.13);
  --grey100: rgba(16, 16, 16, 0.06);
  --silver900: #ffffff;
  --silver400: rgba(255, 255, 255, 0.6);
  --silver50: rgba(255, 255, 255, 0.11);
  --offsetMedium: #e7f1f8;
  // --linksSelections: #1576DB; // Duplicate
  // --secondary: #00B8F5; // Duplicate
  // --positive: #52CB77; // Duplicate
  // --negative: #E63757; // Duplicate
  // --warning: #F8A60A; // Duplicate
  // --primaryOffsetVariant: #D8E7F7; // Duplicate

  // Text
  // --grey900: #101010; // Duplicate
  --grey600: rgba(16, 16, 16, 0.7);
  // --grey500: rgba(16, 16, 16, 0.54); // Duplicate
  --grey300: rgba(16, 16, 16, 0.22);
  // --silver900: #FFFFFF; // Duplicate
  // --silver400: rgba(255, 255, 255, 0.6); // Duplicate
  --silver100: rgba(255, 255, 255, 0.22);
  --silverForever: #ffffff;
  // --linksSelections: #1576DB; // Duplicate
  --linkInverse: #ffffff;
  // --secondary: #00B8F5; // Duplicate
  // --positive: #52CB77; // Duplicate
  // --negative: #E63757; // Duplicate
  // --warning: #F8A60A; // Duplicate

  // Icon
  // --grey900: #101010; // Duplicate
  // --grey600: rgba(16, 16, 16, 0.7); // Duplicate
  // --grey500: rgba(16, 16, 16, 0.54); // Duplicate
  // --grey300: rgba(16, 16, 16, 0.22); // Duplicate
  // --silver900: #FFFFFF; // Duplicate
  // --silver400: rgba(255, 255, 255, 0.6); // Duplicate
  // --silver100: rgba(255, 255, 255, 0.22); // Duplicate
  // --silverForever: #FFFFFF; // Duplicate
  // --linksSelections: #1576DB; // Duplicate
  // --linkInverse: #FFFFFF; // Duplicate
  // --secondary: #00B8F5; // Duplicate
  // --positive: #52CB77; // Duplicate
  // --negative: #E63757; // Duplicate
  // --warning: #F8A60A; // Duplicate

  // Vivid
  --yellow: #fed533;
  --yellowForever: #fed533;
  --yellowLight: #fff6b2;
  --bule: #2f81ed;
  --blueLight: #e3f1ff;
  --turquoise: #a3fff4;
  --lightBlue: #add8e6;

  // Gradient
  --gradientPrimary: #c2daf3;
  --gradientSecondary: #e9e4e4;
  --gradientPurpleLight: #d3d4ff;
  --gradientBlueLight: #d5e8ff;

  //Shimmer
  --shimmer: linear-gradient(
    to right,
    rgba(239, 241, 243, 75%) 2%,
    rgba(226, 226, 226, 30%) 25%,
    rgba(239, 241, 243, 70%) 60%
  );

  //Border
  --border-neutral-variant: rgba(16, 16, 16, 0.1294117647);
}
