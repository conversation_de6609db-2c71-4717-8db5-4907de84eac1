import { useRef, useEffect, useCallback } from 'react';

const SECRET_KEY_MSG = 'Paytmmoney login page';
import crypto from 'crypto-js';

import { COOKIES } from './enums';
import Cookies from '../services/Cookies';
import { getBridge } from './bridgeUtils';

export const isMobile = {
  android() {
    return navigator.userAgent.match(/Android/i) ? 'android' : false;
  },
  iOS() {
    return navigator.userAgent.match(/iPhone|iPad|iPod/i) ? 'ios' : false;
  },
  any() {
    return isMobile.android() || isMobile.iOS() || 'mWeb';
  },
};

function parseQueryString(qs) {
  let queryString = typeof qs !== 'string' ? null : qs.trim();
  if (!queryString) {
    return {};
  }
  queryString = queryString[0] === '?' ? queryString.slice(1) : queryString;

  return queryString
    .split('&')
    .map((p) => p.split('='))
    .reduce((obj, pair) => {
      const [key, value] = pair.map(decodeURIComponent);
      if (key === '' || typeof value === 'undefined') {
        return obj;
      }
      return ({ ...obj, [key]: value });
    }, {});
}

const getUrlParameter = (name, queryString) => {
  const regex = new RegExp(`[\\?&]${name}=([^&#]*)`);
  const results = regex.exec(queryString);
  return results === null ? '' : decodeURIComponent(results[1].replace(/\+/g, ' '));
};

const isH5 = () => {
  try {
    if (localStorage.getItem('isH5') === 1) {
      return true;
    }
    if (
      typeof getBridge() !== 'undefined'
      || getUrlParameter('os', window.location.search)
    ) {
      localStorage.setItem('isH5', 1);
      return true;
    }
    return false;
  } catch (e) {
    return true;
  }
};

const getPlatformValue = () => (isMobile.iOS() ? 'paytm-ios' : 'paytm-android');

const isPhoenixContainer = () => {
  const ua = window.navigator.userAgent;
  return /PhoenixContainer/i.test(ua);
};

function setCookie(name, value, expTime) {
  const expOn = expTime ? new Date(expTime) : new Date(2021, 11, 31);
  const expires = expTime === 0 ? 0 : expOn.toUTCString();
  const path = '/';
  const cookie = `${name}=${value}; expires=${expires}; path=${path}; domain=.paytmmoney.com`;
  document.cookie = cookie;
}

function serializeToQueryString(obj) {
  const str = [];
  Object.keys(obj).forEach((p) => {
    str.push(`${encodeURIComponent(p)}=${encodeURIComponent(obj[p])}`);
  });
  return str.join('&');
}


function getBrowserName() {
  if ((navigator.userAgent.indexOf('Opera') || navigator.userAgent.indexOf('OPR')) != -1) {
    return 'Opera';
  }
  if (navigator.userAgent.indexOf('Edg') != -1) {
    return 'Edge';
  }
  if (navigator.userAgent.indexOf('Chrome') != -1) {
    return 'Chrome';
  } if (navigator.userAgent.indexOf('Safari') != -1) {
    return 'Safari';
  } if (navigator.userAgent.indexOf('Firefox') != -1) {
    return 'Firefox';
  } if ((navigator.userAgent.indexOf('MSIE') != -1) || (!!document.documentMode == true)) {
    return 'IE';
  }
  return 'Unknown';
}

function encryptData(data) {
  const iv = crypto.enc.Base64.parse('');
  const key = crypto.SHA256(SECRET_KEY_MSG);
  let encryptedString = '';
  if (typeof data === 'string') {
    const encData = data.slice();
    encryptedString = crypto.AES.encrypt(encData, key, {
      iv,
      mode: crypto.mode.CBC,
      padding: crypto.pad.Pkcs7,
    });
  } else {
    encryptedString = crypto.AES.encrypt(JSON.stringify(data), key, {
      iv,
      mode: crypto.mode.CBC,
      padding: crypto.pad.Pkcs7,
    });
  }
  return encryptedString.toString();
}

function setFlowType(flowType) {
  const encryptedFlowType = encryptData(flowType);
  const expires = new Date();
  expires.setFullYear(expires.getFullYear() + 1);
  setCookie(COOKIES.FLOW_TYPE, encryptedFlowType, expires);
}

const regExTester = (type) => {
  const regExobj = {
    email: new RegExp(
      [
        '^(([^<>()[\\]\\\\.,;:\\s@"]+(\\.[^<>()[\\]\\\\.,;:\\s@"]+)*',
        ')|(".+"))@((\\[[0-9]{1,3}\\.[0-9]{1,3}\\.[0-9]{1,3}\\.',
        '[0-9]{1,3}\\])|(([a-zA-Z\\-0-9]+\\.)+[a-zA-Z]{2,}))$',
      ].join(''),
    ),
    tel_otp: /^(\+91[- ]?)?\d{10}$/,
    validate_otp: /^\d{6}$/,
  };
  const regExRule = regExobj[type];
  const ex = new RegExp(regExRule);
  return ex;
};

const getDeviceId = () => {
  const deviceId = Cookies.getItem(COOKIES.DEVICE_ID);
  return deviceId;
}

const showLogs = __ENV__ === 'staging';
const log = showLogs ? console.log.bind(console) : () => { };
const errorLog = showLogs ? console.error.bind(console) : () => { };


const doNothing = () => { };

const SW = true;

const NativeBackPressTest = {
  subscribe: callBackFn => {
    const bridgeName = getBridge();
    if (isPhoenixContainer()) {
      if (typeof bridgeName !== 'undefined') {
        bridgeName.subscribe('back', callBackFn);
      }
    } else {
      document.addEventListener('back', callBackFn, false);
    }
  },
  unSubscribe: callBackFn => {
    const bridgeName = getBridge();
    if (isPhoenixContainer()) {
      if (typeof bridgeName !== 'undefined') {
        bridgeName.unsubscribe('back', callBackFn);
      }
    } else {
      document.removeEventListener('back', callBackFn, false);
    }
  },
};

function useNativeBackPress() {
  const stack = useRef([]);
  // TODO: remove global var
  if (typeof window !== "undefined") window.backPressStack = stack;

  const handleBackPress = useCallback(() => {
    if (stack.current.length) {
      stack.current.pop()();
    } else {
      goBack(history);
    }
  }, []);

  const pushStack = callback => {
    stack.current.push(callback);
  };

  const popStack = () => {
    if (stack.current.length) {
      stack.current.pop()();
    }
  };

  const clearStack = () => {
    if (stack.current.length) {
      stack.current = [];
    }
  };

  if (typeof window !== "undefined") window.clearStack = clearStack;

  useEffect(() => {
    NativeBackPressTest.subscribe(handleBackPress);
    return () => {
      log('exit called');
      NativeBackPressTest.unSubscribe(handleBackPress);
    };
  }, [handleBackPress]);

  return {
    stack: stack.current,
    pushStack,
    popStack,
    clearStack,
  };
}

const memoize = (fn) => {
  const cache = {};
  return (...args) => {
    const key = JSON.stringify(args);
    if (!cache[key]) {
      cache[key] = fn(...args);
    }
    return cache[key];
  };
};

const checkIsDraInQueryParams = (uri) => {
  const retUrl = getUrlParameter("returnUrl", uri);
  const decodedUri = decodeURIComponent(retUrl);
  const queryParams = new URLSearchParams(decodedUri);
  if(queryParams.has('isDra')){
    return "dra_web_referall";
  };
  return "organic";
};

const memoizedCheckIsDraInQueryParams = memoize(checkIsDraInQueryParams);

export {
  parseQueryString,
  getUrlParameter,
  isH5,
  getPlatformValue,
  isPhoenixContainer,
  setCookie,
  serializeToQueryString,
  getBrowserName,
  encryptData,
  setFlowType,
  regExTester,
  getDeviceId,
  doNothing,
  useNativeBackPress,
  NativeBackPressTest,
  log,
  errorLog,
  SW,
  memoizedCheckIsDraInQueryParams
};
