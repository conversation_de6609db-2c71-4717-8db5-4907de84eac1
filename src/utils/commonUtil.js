/* eslint-disable no-undef */
import dayjs from 'dayjs';
import get from 'lodash/get';
import queryString from 'query-string';
// eslint-disable-next-line import/no-cycle

import { formatPrice } from '@paytm-money/utils-frontend/utils/numberUtils';
import DeviceInfoProvider from '../provider/DeviceInfoProvider';
import { BUFFER } from './constants';
import {
  getAppVersion,
  getAppVersionCode,
  getH5NativeDeepLinkData,
} from './coreUtil';

export const log = (...args) => {
  // @ts-ignore
  if (__BUILD__ !== 'prod') {
    /* eslint-disable no-console */
    console.log(...args);
  }
};

export const errorLog = (...args) => {
  // @ts-ignore
  if (__BUILD__ !== 'prod') {
    /* eslint-disable no-console */
    console.error(...args);
  }
};

export const getMobileOperatingSystem = () => {
  if (typeof window !== 'undefined') {
    const userAgent = navigator.userAgent || navigator.vendor || window.opera;
    // Windows Phone must come first because its UA also contains “Android”
    if (/windows phone/i.test(userAgent)) {
      return 'windows';
    }
    if (/android/i.test(userAgent)) {
      return 'android';
    }
    // iOS detection from: http://stackoverflow.com/a/9039885/177710
    if (/iPad|iPhone|iPod/.test(userAgent) && !window.MSStream) {
      return 'ios';
    }
    return 'android';
  }
};

export const isIosBuild = () =>
  DeviceInfoProvider.getInfo('device_type') === 'ios' ||
  DeviceInfoProvider.getInfo('device_type') === 'iosapp';

export const getOrigin = () => DeviceInfoProvider.getInfo('origin') || 'PAYTM';

export const isDarkMode = () =>
  DeviceInfoProvider.getInfo('darkmode') === 'true' ||
  DeviceInfoProvider.getInfo('darkmode') === true;

export const generateUniqSerial = (number) =>
  'xxxx-xxxx-xxx-xxxx'.replace(/[x]/g, () => {
    const r = Math.floor(Math.random() * 16);
    return r.toString(number);
  });

export function getCookieValue(name) {
  const b = document.cookie.match(`(^|[^;]+)\\s*${name}\\s*=\\s*([^;]+)`);
  return b ? decodeURIComponent(b.pop()) : '';
}
export const isNonInteger = (val) => val === '.' || !/^[0-9,]*$/.test(val);

export const getTotalAmountWithBuffer = (totalAmount) => {
  const roundedTotalAmount = Math.round(totalAmount);
  const bufferAmount = Math.round(totalAmount * BUFFER);
  const totalAmountWithBuffer = roundedTotalAmount + bufferAmount;
  return totalAmountWithBuffer;
};

export const getDaysBetween = (startDate, endDate) => {
  const startDateFormatted = dayjs(startDate);
  const endDateFormatted = dayjs(endDate);
  const numberOfDays = endDateFormatted.diff(startDateFormatted, 'days');
  return numberOfDays;
};

export function versionCompare(v1, v2, options) {
  if (!v1 || !v2) return -1;
  if (v1 === v2) return 1;
  const lexicographical = options && options.lexicographical;
  const zeroExtend = options && options.zeroExtend;
  let v1parts = v1.split('.');
  let v2parts = v2.split('.');

  function isValidPart(x) {
    return (lexicographical ? /^\d+[A-Za-z]*$/ : /^\d+$/).test(x);
  }

  if (!v1parts.every(isValidPart) || !v2parts.every(isValidPart)) {
    return NaN;
  }

  if (zeroExtend) {
    while (v1parts.length < v2parts.length) v1parts.push('0');
    while (v2parts.length < v1parts.length) v2parts.push('0');
  }

  if (!lexicographical) {
    v1parts = v1parts.map(Number);
    v2parts = v2parts.map(Number);
  }

  // eslint-disable-next-line no-plusplus
  for (let i = 0; i < v1parts.length; ++i) {
    if (v2parts.length === i) {
      return 1;
    }

    // eslint-disable-next-line eqeqeq
    if (v1parts[i] === v2parts[i]) {
      // eslint-disable-next-line no-continue
      continue;
    } else if (v1parts[i] > v2parts[i]) {
      return 1;
    } else {
      return -1;
    }
  }

  // eslint-disable-next-line eqeqeq
  if (v1parts.length !== v2parts.length) {
    return -1;
  }

  return 0;
}

export const renderInitials = (displayName) => {
  const [firstName = '', second = ''] = displayName.split(' ');
  let lastName = '';
  if (lastName) {
    lastName = second;
  }
  return displayName
    ? `${firstName.slice(0, 1).toUpperCase()}${lastName
        .slice(0, 1)
        .toUpperCase()}`
    : 'HI';
};

export const deviceTypeCheck = () => {
  const deviceType = getMobileOperatingSystem();
  DeviceInfoProvider.setInfo('device_type', deviceType);
};

export const setIsDailySipMF = (value) => {
  DeviceInfoProvider.setInfo('isDailySipMF', value);
};

export const getIsDailySipMF = () => DeviceInfoProvider.getInfo('isDailySipMF');

export const setDailySipCohort = (value) => {
  DeviceInfoProvider.setInfo('dailySipCohort', value);
};

export const getDailySipCohort = () =>
  DeviceInfoProvider.getInfo('dailySipCohort');

export const setETFWidgetType = (value) => {
  DeviceInfoProvider.setInfo('etfWidgetType', value);
};

export const getETFWidgetType = () =>
  DeviceInfoProvider.getInfo('etfWidgetType');

export const originCheck = () => {
  const urlSearchParams = new URLSearchParams(window.location.search);
  const queryParams = Object.fromEntries(urlSearchParams.entries());
  let origin = queryParams?.origin;
  if (!origin) {
    origin = 'PAYTMMONEY';
  }
  DeviceInfoProvider.setInfo('origin', String(origin).toUpperCase());
};

export function isEmpty(value) {
  return (
    value === undefined ||
    value === null ||
    (typeof value === 'object' && Object.keys(value).length === 0) ||
    (typeof value === 'string' && value.trim().length === 0)
  );
}

export const getQueryParamsFromString = (str) => {
  const urlSearchParams = new URLSearchParams(str);
  return Object.fromEntries(urlSearchParams.entries());
};

export const getQueryParams = () => {
  const urlSearchParams = new URLSearchParams(window.location.search);
  return Object.fromEntries(urlSearchParams.entries());
};

export function getInitialQueryParams() {
  return DeviceInfoProvider.getInfo('initialQueryParams');
}

export const generateQueryParamsString = (obj = {}) =>
  `?${new URLSearchParams(obj).toString()}`;

export const isPaytm = () => getOrigin() === 'PAYTM';

export const getDeeplinkData = (key) => {
  const query = getH5NativeDeepLinkData();
  return get(queryString.parse(query), key, '');
};

export const getDeeplinkDataOrQueryParam = (key) =>
  getDeeplinkData(key) || getQueryParams()[key];

export function handleThemeChange(theme) {
  if (theme === 'dark') {
    DeviceInfoProvider.setInfo('darkmode', 'true');
    document.documentElement.classList.add('dark');
  } else {
    DeviceInfoProvider.setInfo('darkmode', 'false');
    document.documentElement.classList.remove('dark');
  }
}

export function numberFormatter(num) {
  const formatter = new Intl.NumberFormat('en-IN', {
    style: 'currency',
    currency: 'INR',
    minimumFractionDigits: 0,
    maximumFractionDigits: 2,
    notation: 'compact',
    compactDisplay: 'long',
  });
  return formatter.format(num);
}

export function numberFormatterLimiter({ number, max = 100000 }) {
  if (number > max) return numberFormatter(number);
  return `\u20B9${formatPrice(number)}`;
}

export function memoize(func) {
  // Initialize and empty cache object to hold future values
  const cache = {};

  // Return a function that allows any number of arguments
  return function (...args) {
    // Create a key by joining all the arguments
    const key = args.join('-');

    // Check if cache exists for the key
    if (!cache[key]) {
      // Calculate the value by calling the expensive function if the key didn’t exist
      cache[key] = func.apply(this, args);
    }

    // Return the cached result
    return cache[key];
  };
}

export function removeDuplicates(arr) {
  const unique = arr.reduce((acc, curr) => {
    if (!acc.includes(curr)) acc.push(curr);
    return acc;
  }, []);
  return unique;
}

export const getLastFourChars = (accountNumber) => {
  if (!accountNumber || accountNumber.length < 4) {
    return accountNumber;
  }
  return accountNumber.slice(-4);
};

export const numberToWordsRupees = (num) => {
  const belowTwenty = [
    '',
    'One',
    'Two',
    'Three',
    'Four',
    'Five',
    'Six',
    'Seven',
    'Eight',
    'Nine',
    'Ten',
    'Eleven',
    'Twelve',
    'Thirteen',
    'Fourteen',
    'Fifteen',
    'Sixteen',
    'Seventeen',
    'Eighteen',
    'Nineteen',
  ];
  const tens = [
    '',
    '',
    'Twenty',
    'Thirty',
    'Forty',
    'Fifty',
    'Sixty',
    'Seventy',
    'Eighty',
    'Ninety',
  ];

  function convertToWords(n) {
    if (n === 0) return '';
    if (n < 20) return belowTwenty[n];
    if (n < 100)
      return (
        tens[Math.floor(n / 10)] +
        (n % 10 !== 0 ? ` ${belowTwenty[n % 10]}` : '')
      );
    if (n < 1000)
      return `${belowTwenty[Math.floor(n / 100)]} Hundred${
        n % 100 !== 0 ? ` and ${convertToWords(n % 100)}` : ''
      }`;
    if (n < 100000)
      return `${convertToWords(Math.floor(n / 1000))} Thousand${
        n % 1000 !== 0 ? ` ${convertToWords(n % 1000)}` : ''
      }`;
    if (n < 10000000)
      return `${convertToWords(Math.floor(n / 100000))} Lakh${
        n % 100000 !== 0 ? ` ${convertToWords(n % 100000)}` : ''
      }`;
    if (n < 1000000000)
      return `${convertToWords(Math.floor(n / 10000000))} Crore${
        n % 10000000 !== 0 ? ` ${convertToWords(n % 10000000)}` : ''
      }`;
    if (n < 1000000000000)
      return `${convertToWords(Math.floor(n / 1000000000))} Billion${
        n % 1000000000 !== 0 ? ` ${convertToWords(n % 1000000000)}` : ''
      }`;
    return `${convertToWords(Math.floor(n / 1000000000000))} Trillion${
      n % 1000000000000 !== 0 ? ` ${convertToWords(n % 1000000000000)}` : ''
    }`;
  }

  if (num === 0) return 'Rupees Zero Only';

  // Trim any extra spaces and append 'Only' to the final result
  return `Rupees ${convertToWords(num).trim()} Only`;
};

export const emptyObj = {};
export const setUtmPayload = (url) => {
  const utm_term = get(queryString.parse(url), 'utm_term', '');
  const utm_campaign = get(queryString.parse(url), 'utm_campaign', '');
  const utm_content = get(queryString.parse(url), 'utm_content', '');
  const utm_source = get(queryString.parse(url), 'utm_source', '');
  const utm_medium = get(queryString.parse(url), 'utm_medium', '');

  if (utm_term) {
    sessionStorage.setItem('utm_term', utm_term);
  }
  if (utm_campaign) {
    sessionStorage.setItem('utm_campaign', utm_campaign);
  }
  if (utm_content) {
    sessionStorage.setItem('utm_content', utm_content);
  }
  if (utm_source) {
    sessionStorage.setItem('utm_source', utm_source);
  }
  if (utm_medium) {
    sessionStorage.setItem('utm_medium', utm_medium);
  }
};

export const getUtmPayload = () => {
  const utmPayload = {};

  const utmTerm = sessionStorage.getItem('utm_term');
  const utmCampaign = sessionStorage.getItem('utm_campaign');
  const utmContent = sessionStorage.getItem('utm_content');
  const utmSource = sessionStorage.getItem('utm_source');
  const utmMedium = sessionStorage.getItem('utm_medium');

  if (utmTerm) {
    utmPayload.utm_term = utmTerm;
  }
  if (utmCampaign) {
    utmPayload.utm_campaign = utmCampaign;
  }
  if (utmContent) {
    utmPayload.utm_content = utmContent;
  }
  if (utmSource) {
    utmPayload.utm_source = utmSource;
  }
  if (utmMedium) {
    utmPayload.utm_medium = utmMedium;
  }

  return utmPayload;
};

export function isValidNumber(value) {
  if (!value) return false;
  return !(Number.isNaN(value) || value === Infinity);
}

export function roundValue(value, decimals = 2) {
  if (!isValidNumber(value)) {
    return '0.00';
  }
  const base = 10 ** decimals;
  return (Math.round(value * base) / base).toFixed(decimals);
}

export const toOptionalFixed = (num, digits) =>
  `${Number.parseFloat(num.toFixed(digits))}`;

const multiply = (a, b, x = 1000) => (a * x * (b * x)) / (x * x);

export const floorDecimalValue = (number, toFixed = false) => {
  //  return number
  if (toFixed) {
    return (Math.floor(multiply(number, 100)) / 100).toFixed(2);
  }

  return toOptionalFixed(Math.floor(number * 100) / 100, 2);
};

export function getAbsoluteValue(value, decimals = 2, getAbsolute = true) {
  if (!isValidNumber(value)) {
    return '0.00';
  }
  const roundedValue = roundValue(value, decimals);
  if (!getAbsolute) {
    return roundedValue;
  }
  return roundedValue >= 0
    ? roundedValue
    : roundValue(-1 * roundedValue, decimals);
}

export const generatePaytmDeeplink = (aid, data) =>
  `paytmmp://mini-app?aId=${aid}&data=${data}`;

export const pxToDp = (px) => {
  const dp = px / window.devicePixelRatio; // Convert px to dp
  return Math.round(dp); // Round to nearest integer for better compatibility
};

export function sanitizeAppName(appName) {
  if (!appName) return '';
  const chunk = appName.split(' ');
  if (chunk[chunk.length - 1].toLowerCase() === 'upi') {
    chunk.pop();
  }
  return chunk.join(' ');
}

export function capitalizeFirstLetter(str) {
  if (!str) return str;
  return str.charAt(0).toUpperCase() + str.slice(1);
}

export const getSupportedVersion = (supportedVersion) => {
  try {
    const version = getAppVersion();
    const versionCode = getAppVersionCode();
    const appVersion = `${version}${versionCode ? `.${versionCode}` : ''}`;
    console.log(
      'getSupportedVersion ::: try',
      appVersion,
      supportedVersion,
      versionCompare(appVersion, supportedVersion, { zeroExtend: true }),
    );
    if (appVersion && supportedVersion) {
      return (
        versionCompare(appVersion, supportedVersion, { zeroExtend: true }) >= 0
      );
    }
    return false;
  } catch (e) {
    console.log('getSupportedVersion ::: error', e);
    return false;
  }
};
