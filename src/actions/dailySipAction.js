import { MF_API_URLS } from '../config/urlConfig';
import { AxiosErrorHandler } from '../utils/errorUtils';
import {
  getGenericAppHeaders,
  makeApiGetCall,
  makeApiPostCall,
} from '../utils/apiUtil';
import { getUserId } from '../utils/coreUtil';

export const getFundList = async (pageNumber, type) => {
  const headers = {
    ...getGenericAppHeaders(),
  };
  const url = MF_API_URLS.FETCH_FUNDS(pageNumber, type);
  try {
    const response = await makeApiGetCall({
      headers,
      url,
    });
    const { data } = await response;
    return data;
  } catch (error) {
    AxiosErrorHandler(error);
    return {};
  }
};

export const generateOTP = async (body) => {
  const headers = {
    ...getGenericAppHeaders(),
  };
  const url = MF_API_URLS.GENERATE_OTP(getUserId());
  try {
    const response = await makeApiPostCall({
      headers,
      url,
      body,
    });
    const { data } = await response;
    return data;
  } catch (error) {
    AxiosErrorHandler(error, false, false, false, true);
    return {};
  }
};

export const verifyOTPAndPurchase = async (body) => {
  try {
    const headers = {
      ...getGenericAppHeaders(),
    };
    const url = MF_API_URLS.BUY_MF_V7(getUserId());

    const response = await makeApiPostCall({
      headers,
      url,
      body,
    });
    const { data } = response;
    return data;
  } catch (error) {
    AxiosErrorHandler(error, false, false, false, true);
    return {};
  }
};
