import { useCallback, useContext, useEffect, useMemo, useRef } from 'react';
import NativeBackPressContext from '../contexts/NativeBackPressContext';
import { getBridge } from '../utils/bridgeUtils';
import { emptyObj, log } from '../utils/commonUtil';
import { goBack, isPhoenixContainer } from '../utils/coreUtil';
// import useAppNavigate from './useAppNavigate';
// import { useAppStore } from '../contexts/AppContextProvider';

const NativeBackPressTest = {
  subscribe: (callBackFn) => {
    const bridgeName = getBridge();
    if (isPhoenixContainer()) {
      if (typeof bridgeName !== 'undefined') {
        bridgeName.subscribe('back', callBackFn);
      }
    } else {
      document.addEventListener('back', callBackFn, false);
    }
  },
  unSubscribe: (callBackFn) => {
    const bridgeName = getBridge();
    if (isPhoenixContainer()) {
      if (typeof bridgeName !== 'undefined') {
        bridgeName.unsubscribe('back', callBackFn);
      }
    } else {
      document.removeEventListener('back', callBackFn, false);
    }
  },
};

export const useNativeBackPress = () => {
  const stack = useRef([]);
  // TODO: remove global var

  // const { appNavigate } = useAppNavigate();
  // const { isRemoteApp } = useAppStore() || emptyObj;

  const popStack = useCallback(() => {
    if (stack.current.length) {
      const fn = stack.current.pop();
      if (typeof fn === 'function') {
        fn();
      } else if (fn.current) {
        fn.current();
      }
    }
  }, []);

  const handleBackPress = useCallback(() => {
    if (stack.current.length) {
      popStack();
    } else {
      // goBack(appNavigate);
      goBack();
    }
  }, [/* appNavigate, */ popStack]);

  const pushStack = useCallback((callback) => {
    stack.current.push(callback);
  }, []);

  const clearStack = useCallback(() => {
    if (stack.current.length) {
      stack.current = [];
    }
  }, []);

  useEffect(() => {
    NativeBackPressTest.subscribe(handleBackPress);

    return () => {
      log('exit called');
      // if (!isRemoteApp) {
        NativeBackPressTest.unSubscribe(handleBackPress);
      // }
    };
  }, [handleBackPress]);

  if (typeof window !== 'undefined') {
    window.backPressStack = stack;
    window.clearStack = clearStack;
    window.backPress = popStack;
    window.handleBackPress = handleBackPress;
  }

  return useMemo(
    () => ({
      handleBackPress,
      stack: stack.current,
      pushStack,
      popStack,
      clearStack,
    }),
    [clearStack, handleBackPress, popStack, pushStack],
  );
};

export function useBackPress() {
  const { pushStack, popStack, stack, clearStack, handleBackPress } =
    useContext(NativeBackPressContext);

  return {
    pushStack,
    popStack,
    stack,
    clearStack,
    handleBackPress,
  };
}
