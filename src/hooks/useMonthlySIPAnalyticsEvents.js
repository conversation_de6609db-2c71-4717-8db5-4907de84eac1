import { useUserReadiness } from '../query/generalQuery';
import { sendAnalyticsEventDailySIP } from '../utils/coreUtil';
import { PULSE_STATICS_MONTHLY_SIP } from '../utils/constants';
import { getDailySipCohort } from '../utils/commonUtil';

const useMonthlySIPAnalyticsEvents = () => {
  const { data, isLoading } = useUserReadiness(true);

  if (isLoading) {
    return {
      sendAnalyticsEventMonthlySIP: (params) => {
        sendAnalyticsEventDailySIP({
          verticalName: PULSE_STATICS_MONTHLY_SIP.VERTICAL_NAME,
          screenName: PULSE_STATICS_MONTHLY_SIP.SCREEN_NAME,
          category: PULSE_STATICS_MONTHLY_SIP.CATEGORY,
          label4: getDailySipCohort() || '',
          label6: `irStatus:NA | isUserInvested:NA`,
          ...params,
        });
      },
    };
  }

  const { hasInvested, irStatus } = data || {
    hasInvested: null,
    irStatus: null,
  };

  return {
    sendAnalyticsEventMonthlySIP: (params) => {
      sendAnalyticsEventDailySIP({
        verticalName: PULSE_STATICS_MONTHLY_SIP.VERTICAL_NAME,
        screenName: PULSE_STATICS_MONTHLY_SIP.SCREEN_NAME,
        category: PULSE_STATICS_MONTHLY_SIP.CATEGORY,
        label4: getDailySipCohort() || '',
        label6: `irstatus:${irStatus} | isinvestedstatus:${hasInvested}`,
        ...params,
      });
    },
  };
};

export { useMonthlySIPAnalyticsEvents };
