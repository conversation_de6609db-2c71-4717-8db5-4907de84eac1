import { useUserReadiness, useIrData } from '../query/generalQuery';
import {
  IR_STATUS_ENUM,
  PULSE_STATICS_FIRST_CARDS,
  VERTICAL_NAME,
  HAS_INVESTED_STATUS,
} from '../utils/constants';
import {
  sendAnalyticsEventFirstCard,
  sendAnalyticsEvent,
  sendAnalyticsEventFirstStock,
  sendAnalyticsEventFirstTradeProgressCard,
  sendAnalyticsEventWidget,
} from '../utils/coreUtil';

const useAnalyticsEventForFirstCards = () => {
  const { data, isLoading } = useUserReadiness(true);
  const { data: irData, isLoading: isIrLoading } = useIrData(true);

  if (isLoading && isIrLoading) {
    return {
      sendAnalyticsEventFirstCard: (params) => {
        sendAnalyticsEventFirstCard({
          verticalName: PULSE_STATICS_FIRST_CARDS.VERTICAL_NAME,
          screenName: PULSE_STATICS_FIRST_CARDS.SCREEN_NAME,
          category: PULSE_STATICS_FIRST_CARDS.CATEGORY,
          label6: `irStatus:NA | isUserInvested:NA`,
          ...params,
        });
      },
    };
  }

  const { hasInvested, irStatus: status } = data || {
    hasInvested: null,
    irStatus: null,
  };

  let irStatus = status;
  if (
    ![
      IR_STATUS_ENUM.ACTIVE,
      IR_STATUS_ENUM.DORMANCY_IN_PROGRESS,
      IR_STATUS_ENUM.DORMANT_REVOKED,
      IR_STATUS_ENUM.REKYC_IN_PROGRESS,
    ].includes(status)
  ) {
    irStatus = irData?.data?.irStatus;
  }

  return {
    sendAnalyticsEventFirstCard: (params) => {
      sendAnalyticsEventFirstCard({
        verticalName: PULSE_STATICS_FIRST_CARDS.VERTICAL_NAME,
        screenName: PULSE_STATICS_FIRST_CARDS.SCREEN_NAME,
        category: PULSE_STATICS_FIRST_CARDS.CATEGORY,
        label6: `irstatus:${irStatus} | isinvestedstatus:${hasInvested}`,
        ...params,
      });
    },
  };
};

const useAnalyticsEventForFirstStock = () => {
  const { data, isLoading } = useUserReadiness(true);
  const { data: irData, isLoading: isIrLoading } = useIrData(true);

  if (isLoading && isIrLoading) {
    return {
      sendAnalyticsEventFirstStock: (params) => {
        sendAnalyticsEventFirstStock({
          verticalName: PULSE_STATICS_FIRST_CARDS.VERTICAL_NAME,
          screenName: PULSE_STATICS_FIRST_CARDS.SCREEN_NAME,
          category: PULSE_STATICS_FIRST_CARDS.CATEGORY,
          label6: `irStatus:NA | isUserInvested:NA`,
          ...params,
        });
      },
    };
  }

  const { hasInvested, irStatus: status } = data || {
    hasInvested: null,
    irStatus: null,
  };

  let irStatus = status;
  if (
    ![
      IR_STATUS_ENUM.ACTIVE,
      IR_STATUS_ENUM.DORMANCY_IN_PROGRESS,
      IR_STATUS_ENUM.DORMANT_REVOKED,
      IR_STATUS_ENUM.REKYC_IN_PROGRESS,
    ].includes(status)
  ) {
    irStatus = irData?.data?.irStatus;
  }

  return {
    sendAnalyticsEventFirstStock: (params) => {
      sendAnalyticsEventFirstStock({
        verticalName: PULSE_STATICS_FIRST_CARDS.VERTICAL_NAME,
        screenName: PULSE_STATICS_FIRST_CARDS.SCREEN_NAME,
        category: PULSE_STATICS_FIRST_CARDS.CATEGORY,
        label6: `irstatus:${irStatus} | isinvestedstatus:${hasInvested}`,
        ...params,
      });
    },
  };
};
const useAnalyticsEvent = (firstTrade = false) => {
  const { data, isLoading } = useUserReadiness(!firstTrade);
  const { data: irData, isLoading: isIrLoading } = useIrData(!firstTrade);

  if (firstTrade) {
    return {
      sendAnalyticsEvent: () => {},
    };
  }

  if (isLoading && isIrLoading)
    return {
      sendAnalyticsEvent: (...params) => {
        sendAnalyticsEvent(
          VERTICAL_NAME,
          ...params,
          `irStatus=NA | isUserInvested=NA`,
        );
      },
    };

  const { hasInvested, irStatus: status } = data || {
    hasInvested: null,
    irStatus: null,
  };

  let irStatus = status;
  if (
    ![
      IR_STATUS_ENUM.ACTIVE,
      IR_STATUS_ENUM.DORMANCY_IN_PROGRESS,
      IR_STATUS_ENUM.DORMANT_REVOKED,
      IR_STATUS_ENUM.REKYC_IN_PROGRESS,
    ].includes(status)
  ) {
    irStatus = irData?.data?.irStatus;
  }
  const isUserInvested = hasInvested
    ? hasInvested === HAS_INVESTED_STATUS.HAS_INVESTED
    : 'NA';
  /*
   * params ->
   * 1. screenName,
   * 2. categories,
   * 3. userAction
   * 4. label2
   * 5. label5
   * */

  return {
    sendAnalyticsEvent: (...params) => {
      sendAnalyticsEvent(
        VERTICAL_NAME,
        ...params,
        `irStatus=${irStatus} | isUserInvested=${isUserInvested}`,
      );
    },
  };
};

const useAnalyticsEventForFirstTradeProgressCard = () => ({
  sendAnalyticsEvent: (...params) => {
    sendAnalyticsEventFirstTradeProgressCard(...params);
  },
});

const useAnalyticsEventForWidget = () => {
  const { data, isLoading } = useUserReadiness(true);
  const { data: irData, isLoading: isIrLoading } = useIrData(true);

  if (isLoading && isIrLoading) {
    return {
      sendAnalyticsEventWidget: (params) => {
        sendAnalyticsEventWidget({
          verticalName: PULSE_STATICS_FIRST_CARDS.VERTICAL_NAME,
          screenName: PULSE_STATICS_FIRST_CARDS.SCREEN_NAME,
          category: PULSE_STATICS_FIRST_CARDS.CATEGORY,
          label6: `irStatus:NA | isUserInvested:NA`,
          ...params,
        });
      },
    };
  }

  const { hasInvested, irStatus: status } = data || {
    hasInvested: null,
    irStatus: null,
  };

  let irStatus = status;
  if (
    ![
      IR_STATUS_ENUM.ACTIVE,
      IR_STATUS_ENUM.DORMANCY_IN_PROGRESS,
      IR_STATUS_ENUM.DORMANT_REVOKED,
      IR_STATUS_ENUM.REKYC_IN_PROGRESS,
    ].includes(status)
  ) {
    irStatus = irData?.data?.irStatus;
  }

  return {
    sendAnalyticsEventWidget: (params) => {
      sendAnalyticsEventWidget({
        verticalName: PULSE_STATICS_FIRST_CARDS.VERTICAL_NAME,
        screenName: PULSE_STATICS_FIRST_CARDS.SCREEN_NAME,
        category: PULSE_STATICS_FIRST_CARDS.CATEGORY,
        label6: `irstatus:${irStatus} | isinvestedstatus:${hasInvested}`,
        ...params,
      });
    },
  };
};

export {
  useAnalyticsEventForFirstCards,
  useAnalyticsEvent,
  useAnalyticsEventForFirstTradeProgressCard,
  useAnalyticsEventForFirstStock,
  useAnalyticsEventForWidget,
};
