import { useUserReadiness } from '../query/generalQuery';
import { sendAnalyticsEventDailySIP } from '../utils/coreUtil';
import { PULSE_STATICS_DAILY_SIP } from '../utils/constants';
import { getIsDailySipMF, getDailySipCohort } from '../utils/commonUtil';

const useDailySIPAnalyticsEvents = () => {
  const { data, isLoading } = useUserReadiness(true);

  if (isLoading) {
    return {
      sendAnalyticsEventDailySIP: (params) => {
        sendAnalyticsEventDailySIP({
          verticalName: PULSE_STATICS_DAILY_SIP.VERTICAL_NAME,
          screenName: PULSE_STATICS_DAILY_SIP.SCREEN_NAME,
          category: getIsDailySipMF()
            ? PULSE_STATICS_DAILY_SIP.CATEGORY_MF
            : PULSE_STATICS_DAILY_SIP.CATEGORY,
          label4: getDailySipCohort() || '',
          label6: `irStatus:NA | isUserInvested:NA`,
          ...params,
        });
      },
    };
  }

  const { hasInvested, irStatus } = data || {
    hasInvested: null,
    irStatus: null,
  };

  return {
    sendAnalyticsEventDailySIP: (params) => {
      sendAnalyticsEventDailySIP({
        verticalName: PULSE_STATICS_DAILY_SIP.VERTICAL_NAME,
        screenName: PULSE_STATICS_DAILY_SIP.SCREEN_NAME,
        category: getIsDailySipMF()
          ? PULSE_STATICS_DAILY_SIP.CATEGORY_MF
          : PULSE_STATICS_DAILY_SIP.CATEGORY,
        label4: getDailySipCohort() || '',
        label6: `irstatus:${irStatus} | isinvestedstatus:${hasInvested}`,
        ...params,
      });
    },
  };
};

export { useDailySIPAnalyticsEvents };
