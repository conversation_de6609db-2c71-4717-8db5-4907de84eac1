import { useUserReadiness } from '../query/generalQuery';
import { sendAnalyticsEventDailySIP } from '../utils/coreUtil';
import { PULSE_STATICS_ETF_CARD } from '../utils/constants';

const useETFAnalyticsEvent = ({ widgetId, subCohortId }) => {
  const { data, isLoading } = useUserReadiness(true);

  if (isLoading) {
    return {
      sendAnalyticsEventETF: (params) => {
        sendAnalyticsEventDailySIP({
          verticalName: PULSE_STATICS_ETF_CARD.VERTICAL_NAME,
          screenName: PULSE_STATICS_ETF_CARD.SCREEN_NAME,
          category: PULSE_STATICS_ETF_CARD.CATEGORY,
          label2: `${widgetId}, ${widgetId}`,
          label4: subCohortId || '',
          label6: `irStatus:NA | isUserInvested:NA`,
          ...params,
        });
      },
    };
  }

  const { hasInvested, irStatus } = data || {
    hasInvested: null,
    irStatus: null,
  };

  return {
    sendAnalyticsEventETF: (params) => {
      sendAnalyticsEventDailySIP({
        verticalName: PULSE_STATICS_ETF_CARD.VERTICAL_NAME,
        screenName: PULSE_STATICS_ETF_CARD.SCREEN_NAME,
        category: PULSE_STATICS_ETF_CARD.CATEGORY,
        label2: `${widgetId}, ${widgetId}`,
        label4: subCohortId || '',
        label6: `irstatus:${irStatus} | isinvestedstatus:${hasInvested}`,
        ...params,
      });
    },
  };
};

export { useETFAnalyticsEvent };
