import { useState, useRef, useEffect, useCallback } from 'react';
import PropTypes from 'prop-types';
import cx from 'classnames';

import Icon, { ICONS_NAME } from '../Icon/index';

import styles from './index.scss';

const Dropdown = ({
  displayValue,
  options,
  handleSelection,
  title,
  customDropdownHeader,
  customDropdownList,
  isToggleDisabled,
  titleClassName,
  onDropDownVisibilityChange,
  downArrow,
}) => {
  const [displayDropdown, setDisplayDropdown] = useState(false);
  const dropdownWrapperRef = useRef(null);

  const handleOptionClick = (selectedVal, selectedValCode, selectedType) => {
    setDisplayDropdown(false);
    onDropDownVisibilityChange(false);
    handleSelection(selectedVal, selectedValCode, selectedType);
  };

  const toggleDropdown = (event) => {
    if (!isToggleDisabled) {
      event.stopPropagation();
      setDisplayDropdown(!displayDropdown);
      onDropDownVisibilityChange(!displayDropdown);
    }
  };

  const handleClickOutside = useCallback(
    (event) => {
      if (
        dropdownWrapperRef.current &&
        !dropdownWrapperRef.current.contains(event.target) &&
        displayDropdown
      ) {
        if (displayDropdown) {
          setDisplayDropdown(false);
          onDropDownVisibilityChange(false);
        }
      }
    },
    [displayDropdown],
  );

  useEffect(() => {
    document.addEventListener('click', handleClickOutside);
    return () => {
      document.removeEventListener('click', handleClickOutside);
    };
  }, [handleClickOutside]);

  return (
    <>
      {displayValue && (
        <div
          className={cx({
            [titleClassName]: titleClassName,
            [styles.title]: true,
          })}
        >
          {title}
        </div>
      )}
      <div ref={dropdownWrapperRef} className={styles.dropdownContainer}>
        <div
          onClick={toggleDropdown}
          className={cx({
            [customDropdownHeader]: customDropdownHeader,
            [styles.dropdownHeader]: true,
            [styles.dropdown]: displayValue,
          })}
        >
          <div
            className={cx(styles.selectedVal, {
              [styles.noSelectedVal]: !displayValue,
            })}
          >
            {displayValue || title}
          </div>
          <div>
            {downArrow || (
              <Icon
                name={
                  displayDropdown
                    ? ICONS_NAME.BLACK_ARROW_UP
                    : ICONS_NAME.BLACK_ARROW_DOWN
                }
                width="13px"
              />
            )}
          </div>
        </div>
        {displayDropdown && (
          <div
            className={cx(styles.optionsList, {
              [customDropdownList]: customDropdownList,
            })}
          >
            {options.map((option) => (
              <div
                key={option.code}
                id={option.code}
                onClick={() =>
                  handleOptionClick(
                    option?.name || option?.displayName,
                    option?.code,
                    option?.type,
                  )
                }
                className={styles.option}
              >
                {option?.name || option?.displayName}
              </div>
            ))}
          </div>
        )}
      </div>
    </>
  );
};

Dropdown.propTypes = {
  displayValue: PropTypes.string,
  options: PropTypes.array,
  handleSelection: PropTypes.func,
  customDropdownList: PropTypes.string,
  customDropdownHeader: PropTypes.string,
  titleClassName: PropTypes.string,
  isToggleDisabled: PropTypes.bool,
  onDropDownVisibilityChange: PropTypes.func,
};

Dropdown.defaultProps = {
  displayValue: '',
  options: [],
  handleSelection: () => {},
  customDropdownList: '',
  customDropdownHeader: '',
  titleClassName: '',
  isToggleDisabled: false,
  onDropDownVisibilityChange: () => {},
};

export default Dropdown;
