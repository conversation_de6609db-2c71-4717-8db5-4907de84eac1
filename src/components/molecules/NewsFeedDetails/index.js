import cx from 'classnames';
import ArrowLeftIcon from '@paytm-h5-common/paytm_common_ui/icons/system/nav/ArrowLeft';
import FallbackCompanyIcon from '@assets/icons/company_fallback_icon.svg';
import StockChange from '../../organisms/StockChange/StockChange';
import CompanyIcon from '../../atoms/CompanyIcon/CompanyIcon';
import styles from './NewsFeedDetails.scss';
import { isDarkMode } from '../../../utils/commonUtil';
import { handleBuySellClick } from '../../../pages/NewsFeedWidgetPage/newsUtils';
import { isPaytmMoney } from '../../../utils/coreUtil';
import { openDeepLinkPaytmMoney } from '../../../utils/bridgeUtils';

const NewsFeedDrawer = ({
  selectedNews,
  // handleBuySellClick,
  companyPageNavigation: companyNavigationWidget,
  navigateTo,
  history,
}) => {
  if (!selectedNews) {
    return null;
  }

  const companyPageNavigation = (stock) => {
    console.log('companyPageNavigation :: stock', stock);
    if (companyNavigationWidget) {
      companyNavigationWidget(stock.pml_id);
    } else {
      const { instrument, pml_id } = stock || {};
      const instrumentType =
        instrument === 'ES' ? 'company' : instrument?.toLowerCase();
      const url = `https://paytmmoney.com/stocks/${instrumentType}/${pml_id}`;
      openDeepLinkPaytmMoney(url);
    }
  };

  return (
    <div className={styles.newsDetail}>
      {' '}
      {/* Main container, potentially needs adjustment based on final structure */}
      <div className={styles.drawerHeader}>
        {/* <div
          // className={styles.drawerHandle}
          onTouchStart={handleDrawerDrag}
          onTouchMove={(e) => {
            const touch = e.touches[0];
            const startY = e.target.getBoundingClientRect().top;
            const dragDistance = touch.clientY - startY;
            if (dragDistance > 100) {
              handleDrawerClose();
            }
          }}
        /> */}
        <div className={styles.headerContent}>
          <div className={styles.companyLogo}>
            <CompanyIcon
              name={selectedNews.pml_id}
              type="stocks"
              url={selectedNews.companyLogo}
              fallbackImg={FallbackCompanyIcon}
            />
          </div>
          <div
            className={styles.titleSection}
            onClick={() => companyPageNavigation(selectedNews)}
          >
            <div className={styles.titleRow}>
              <span className={styles.title}>{selectedNews.companyName}</span>
              <div className={styles.arrowIcon}>
                <ArrowLeftIcon />
              </div>
            </div>
            <div className={styles.priceInfo}>
              <span className={styles.price}>{selectedNews.price}</span>
              <StockChange
                exchange={selectedNews.exchange}
                segment={selectedNews.segment}
                securityId={selectedNews.security_id}
                instrumentType={selectedNews.instrument}
                id={selectedNews.pml_id}
              />
            </div>
          </div>
        </div>
      </div>
      <div className={styles.newsDetailBody}>
        {' '}
        {/* Scrollable content body */}
        {/* Using the structure from the latest user changes */}
        <div className={styles.newsDetailContent}>
          <div
            className={cx(styles.hotNewsContainer, {
              [styles.hotNewsContainerDark]: isDarkMode(),
              [styles.hotNewsContainerLight]: !isDarkMode(),
            })}
          >
            {' '}
            {/* New div based on user changes */}
            {selectedNews.isHotNews && (
              <div
                className={cx(styles.hotNewsTag, {
                  [styles.hotNewsTagBgDark]: isDarkMode(),
                  [styles.hotNewsTagBgLight]: !isDarkMode(),
                })}
              >
                <span>🔥 Hot News</span>
              </div>
            )}
            <div className={styles.newsHeader}>
              <div className={styles.newsTitle}>{selectedNews.news}</div>
              <div className={styles.timestamp}>{selectedNews.timestamp}</div>
              {selectedNews.caption ? (
                <div className={styles.fullNews}>{selectedNews.caption}</div>
              ) : null}
            </div>
          </div>
          {selectedNews?.fullNews && (
            <div className={styles.fullNews}>{selectedNews.fullNews}</div>
          )}
        </div>
      </div>
      <div className={styles.actionButtons}>
        {' '}
        {/* Sticky footer */}
        <button
          type="button"
          className={`${styles.button} ${styles.sell}`}
          onClick={() =>
            handleBuySellClick(selectedNews, 'S', navigateTo, history)
          }
        >
          Sell
        </button>
        <button
          type="button"
          className={`${styles.button} ${styles.buy}`}
          onClick={() =>
            handleBuySellClick(selectedNews, 'B', navigateTo, history)
          }
        >
          Buy
        </button>
      </div>
    </div>
  );
};

export default NewsFeedDrawer;
