import { BottomSheet } from '@paytm-h5-common/paytm_common_ui';
import { useEffect } from 'react';
import { useBackPress } from '../../../hooks/useNativeBackPress';
import { useDrawer } from './useDrawer';

const Drawer = ({ children, active, triggerClose, ...props }) => {
  const { pushStack, popStack } = useBackPress();

  useEffect(() => {
    if (active) {
      console.log('drawer :: triggerClose', active, triggerClose);
      document.body.style.overflow = 'hidden';
      pushStack(triggerClose);
    } else {
      document.body.style.overflow = 'initial';
    }
    return () => {
      document.body.style.overflow = 'initial';
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [active]);

  return (
    <BottomSheet active={active} triggerClose={popStack} {...props}>
      {children}
    </BottomSheet>
  );
};

export default Drawer;
export { useDrawer };
