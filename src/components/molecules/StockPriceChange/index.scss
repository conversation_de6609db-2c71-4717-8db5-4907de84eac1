.stockPrice {
  font-size: 16px;
  font-weight: 500;
  line-height: 1.43;
  color: var(--text-neutral-strong);
  display: inline;
}
@media only screen and (max-width: 375px) {
  .reminderStockPrice {
    display: inline-flex;
    gap: 4px;
  }
}

.buyPrice {
  color: var(--text-neutral-strong);
  line-height: 20px;
}

.ltp {
  display: inline-flex;
}
.ltpProfit {
  color: var(--text-positive-strong);
  line-height: 20px;
  font-weight: 400;
}

.ltpLoss {
  color: var(--text-negative-strong);
  line-height: 20px;
  font-weight: 400;
}

.ltpZero {
  color: var(--text-neutral-medium);
  line-height: 20px;
  font-weight: 400;
}

.percentChange {
  margin: auto 6px;
}
.priceShimmerWrapper {
  display: inline-flex;
}
@media only screen and (max-width: 375px) {
  .reminderPercentChange {
    margin: auto;
  }
}