import React, { useState, useRef, useEffect } from 'react';
import { Loader, RadioButton } from '@paytm-h5-common/paytm_common_ui';

import InfiniteScroll from '../../molecules/InfiniteScroll';
import Drawer from '../../molecules/Drawer/Drawer';
import Icon, { ICONS_NAME } from '../../molecules/Icon/index';

import { getFundList } from '../../../actions/dailySipAction';

import { formatPrice } from '../../molecules/Prices';
import { isPaytmMoney } from '../../../utils/coreUtil';
import { exitApp } from '../../../utils/bridgeUtils';
import { useBackPress } from '../../../hooks/useNativeBackPress';
import { useDailySIPAnalyticsEvents } from '../../../hooks/useDailySIPAnalyticsEvents';

import styles from './FundSelectionPopup.scss';
import { convertToLakhsOrCrore } from '../../../pages/SipCard/utils';
import { PULSE_STATICS } from './enums';

const allowedRanges = new Set(['3y', '1y', '6m', '3m', '1m']);

const FundSelectionPopup = ({
  isOpen = false,
  preSelectedFund,
  setFund,
  onClose,
}) => {
  const [funds, setFunds] = useState([]);
  const [selectedFund, setSelectedFund] = useState(null);
  const [loading, setLoading] = useState(false);
  const [hasMoreData, setHasMoreData] = useState(true);
  const [pageNumber, setPageNumber] = useState(1);
  const infiniteScrollRef = useRef(null);

  const { sendAnalyticsEventDailySIP } = useDailySIPAnalyticsEvents();
  const { popStack, stack } = useBackPress();

  const fetchFunds = async () => {
    if (hasMoreData && !loading) {
      setLoading(true);
      const { data } = await getFundList(pageNumber, 'DAILY');

      setLoading(false);

      const transformedFunds = data.results.map((scheme, index) => ({
        id: funds.length + index + 1,
        ...scheme,
      }));

      const newFundList = [...funds, ...transformedFunds];
      setFunds(newFundList);
      setHasMoreData(data.hasNextPage);
      setPageNumber(pageNumber + 1);

      if (newFundList.length > 0 && !selectedFund) {
        let selectedIndex = newFundList.findIndex(
          ({ isin }) => isin === preSelectedFund?.isin,
        );
        if (selectedIndex === -1) selectedIndex = 0;
        setSelectedFund(newFundList[selectedIndex || 0].id);
      }
    }
  };

  useEffect(() => {
    if (isOpen && preSelectedFund) {
      fetchFunds();

      sendAnalyticsEventDailySIP({
        action: PULSE_STATICS.ACTION.PAGE_LOAD,
      });
    }
  }, [isOpen, preSelectedFund]);

  const formatReturns = (returns) => {
    if (!returns?.length)
      return (
        <p className={styles.fundReturns}>
          <span>Abs. Returns</span>
          <span>N/A</span>
        </p>
      );
    for (let i = returns.length - 1; i >= 0; i -= 1) {
      if (allowedRanges.has(returns[i].name)) {
        return (
          <p className={styles.fundReturns}>
            <span>{returns[i].name.toUpperCase()} Abs. Returns</span>
            <span
              className={
                returns[i].percentage < 0 ? styles.negative : styles.positive
              }
            >
              {`${formatPrice(returns[i].percentage || 0, 2, false, false)}%`}
            </span>
          </p>
        );
      }
    }
    return (
      <p className={styles.fundReturns}>
        <span>Abs. Returns</span>
        <span>N/A</span>
      </p>
    );
  };

  const handleSelect = (id) => {
    setSelectedFund(id);
  };

  const closePopup = () => {
    const fund = funds.find(({ id }) => id === selectedFund);
    if (isPaytmMoney()) {
      localStorage.setItem('selectedFund', JSON.stringify(fund));
      exitApp();
    } else {
      setFund(fund);
      onClose();
      stack.pop();
    }

    sendAnalyticsEventDailySIP({
      action:
        fund?.isin === preSelectedFund?.isin
          ? PULSE_STATICS.ACTION.DEFAULT_SELECT_FUND
          : PULSE_STATICS.ACTION.NO_DEFAULT_SELECT_FUND,
    });
  };

  const onPopupClose = () => {
    sendAnalyticsEventDailySIP({
      action: PULSE_STATICS.ACTION.ON_CLOSE,
    });
    onClose();
  };

  return (
    <Drawer
      active={isOpen}
      triggerClose={onPopupClose}
      showGrabber
      showCloseIcon={false}
      primaryButton={{ label: 'Select Fund', onClick: closePopup }}
      customClass={styles.fundSelectionPopupContainer}
    >
      <h1 className={styles.title}>Popular Daily SIP Mutual Funds</h1>
      <div className={styles.scrollContainer} ref={infiniteScrollRef}>
        {loading ? (
          <div className={styles.loadingContainer}>
            <Loader size="medium" />
          </div>
        ) : (
          <InfiniteScroll
            pageStart={1}
            initialLoad={!loading}
            loadMore={/* fetchFunds */ () => {}}
            hasMore={hasMoreData}
            useWindow={false}
            useCapture={false}
            threshold={500}
            className={styles.fundList}
            getScrollParent={() => infiniteScrollRef.current}
          >
            {funds.map((fund) => (
              <React.Fragment key={fund.id}>
                <div
                  className={`${styles.fundCard} ${selectedFund === fund.id ? styles.selected : ''}`}
                  onClick={() => handleSelect(fund.id)}
                >
                  <div className={styles.fundHeader}>
                    <Icon url={fund.amcLogo} width={40} />
                    <div className={styles.fundInfo}>
                      <div className={styles.fundName}>{fund.schemeName}</div>
                      <p className={styles.fundType}>
                        {fund.category} • {fund.subCategory}
                      </p>
                    </div>
                    <RadioButton
                      checked={selectedFund === fund.id}
                      onChecked={() => handleSelect(fund.id)}
                      customClass={styles.radioButton}
                    />
                  </div>
                  <div className={styles.fundDetails}>
                    <div>
                      <p className={styles.fundRating}>
                        <span>Value Research Rating</span>
                        <span className={styles.stars}>
                          {fund?.rating ? (
                            <span className={styles.stars}>
                              {Array(fund.rating || 0)
                                .fill('')
                                .map((_, index) => (
                                  <Icon
                                    name={ICONS_NAME.RATING_STAR}
                                    width={16}
                                    key={index}
                                  />
                                ))}
                            </span>
                          ) : (
                            <span className={styles.fundRating}>N/A</span>
                          )}
                        </span>
                      </p>
                    </div>
                    <div>
                      {formatReturns(fund.fundReturns?.absoluteReturns)}
                    </div>
                  </div>
                </div>
                <div className={styles.fundInvestorsContainer}>
                  {fund?.estimatedFutureValue ? (
                    <p className={styles.sipWealthPlanText}>
                      ₹{fund?.applicableSipAmount?.[0]} Daily to{' '}
                      <p className={styles.finalWealthAmtText}>
                        ₹
                        {convertToLakhsOrCrore(
                          fund?.estimatedFutureValue,
                          fund?.applicableSipAmount?.[0],
                        )}
                      </p>{' '}
                      * - Your 30 year Wealth Plan 🚀
                    </p>)
                        : ''}
                </div>
              </React.Fragment>
            ))}
          </InfiniteScroll>
        )}
      </div>
    </Drawer>
  );
};

export default FundSelectionPopup;
