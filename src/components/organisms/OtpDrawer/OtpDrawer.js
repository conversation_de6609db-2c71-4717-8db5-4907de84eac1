import React, { useState, useRef, useEffect } from 'react';
import cx from 'classnames';
import { Button } from '@paytm-h5-common/paytm_common_ui';

import { getFormatedData, otpBody } from './OtpUtils';
import {
  generateOTP,
  verifyOTPAndPurchase,
} from '../../../actions/dailySipAction';
import { REGEX } from '../../../config/config';
import {
  OTP_ERR,
  OTP_ERROR_MESSAGES,
} from '../../../config/mfDailySipSelectAmount';
import { useSnackbar } from '../../../contexts/SnackbarProvider';
import { APPEARANCE_TYPES } from '../../../utils/constants';
import { PULSE_STATICS } from './enums';
import { useDailySIPAnalyticsEvents } from '../../../hooks/useDailySIPAnalyticsEvents';

import styles from './OtpDrawer.scss';

const OtpDrawer = ({
  isOpen,
  setIsOpen,
  otpTransactionDetails,
  otpMessage,
  setOtpMessage,
  redirectToAutoPay,
}) => {
  const [otp, setOtp] = useState('');
  const [error, setError] = useState('');
  const { showSnackbar } = useSnackbar();
  const [timer, setTimer] = useState(30);
  const [loader, showLoader] = useState(false);
  const inputRef = useRef(null);
  const containerRef = useRef(null);

  const { sendAnalyticsEventDailySIP } = useDailySIPAnalyticsEvents();

  useEffect(() => {
    if (isOpen) {
      inputRef.current?.focus();

      sendAnalyticsEventDailySIP({
        action: PULSE_STATICS.ACTION.PAGE_LOAD,
        event: PULSE_STATICS.OPEN_SCREEN_EVENT,
      });
    }
  }, [isOpen]);

  useEffect(() => {
    const handleFocus = () => {
      setTimeout(() => {
        window.scrollTo({
          top: document.body.scrollHeight,
          behavior: 'smooth',
        });
      }, 300);
    };

    inputRef.current?.addEventListener('focus', handleFocus);
    return () => inputRef.current?.removeEventListener("focus", handleFocus);
  }, []);

  // const handleFocus = () => {
  //   window.scrollTo({ top: document.body.scrollHeight });
  // };

  // const closeOtpDrawer = () => {
  //   setIsOpen(false);
  // };

  const handleOtpChange = (e) => {
    const value = e.target.value;
    if (!REGEX.VALIDATE_NUMBER.test(String(value))) {
      showSnackbar(OTP_ERR.VALIDATION_ERR);
    } else if (otpMessage) {
      setOtpMessage('');
    }

    setOtp(value);
  };

  useEffect(() => {
    if (timer > 0) {
      const countdown = setTimeout(() => setTimer(timer - 1), 1000);
      return () => clearTimeout(countdown);
    }
  }, [timer]);

  const handleValidateOTPResponse = (result) => {
    if (result?.meta?.code === 'PM_TRXN_SC_200101') {
      setIsOpen(false);
      redirectToAutoPay(false, result?.data);
    } else if (
      ['PM_OTP_EC_400403', 'PM_OTP_EC_400408', 'PM_OTP_EC_400401'].includes(
        result?.meta?.code,
      )
    ) {
      setError(OTP_ERROR_MESSAGES[result?.meta?.code]);
    } else {
      showSnackbar(
        OTP_ERROR_MESSAGES[result?.meta?.code] || OTP_ERROR_MESSAGES.DEFAULT,
      );
    }
  };

  const handleProceedClick = async () => {
    try {
      sendAnalyticsEventDailySIP({
        action: PULSE_STATICS.ACTION.ON_CTA_CLICK,
      });

      showLoader(true);
      const body = getFormatedData(otpTransactionDetails, otp);
      const result = await verifyOTPAndPurchase(body);

      showLoader(false);
      handleValidateOTPResponse(result);
    } catch (err) {
      showLoader(false);
      handleValidateOTPResponse(err);
    }
    setOtp('');
  };

  const handleResendOtp = async () => {
    try {
      const body = otpBody(otpTransactionDetails);
      const result = await generateOTP(body);

      if (result?.meta?.code !== 'PM_TRXN_SC_112') {
        showSnackbar(OTP_ERROR_MESSAGES[result?.meta?.code]);
      } else {
        showSnackbar(
          OTP_ERROR_MESSAGES.OTP_SENT_SUCCESS,
          APPEARANCE_TYPES.SUCCESS,
        );
        inputRef.current?.focus();
      }
    } catch (err) {
      showSnackbar(OTP_ERROR_MESSAGES.DEFAULT);
    }
    setOtp('');
    setError('');
  };

  // const handlePaste = (event) => {
  //   const paste = event.clipboardData.getData('text');
  //   handleOtpChange(paste);
  // };

  const handleResend = () => {
    setTimer(30); // Reset Timer
    handleResendOtp();
  };

  return (
    isOpen && (
      <div className={styles.otpInputWrapper}>
        <div className={styles.otpInputContainer}>
          <input
            type="text"
            inputMode="numeric"
            autoComplete="one-time-code"
            ref={inputRef}
            value={otp}
            onChange={handleOtpChange}
            className={styles.hiddenInputContainer}
          />
          <div
            ref={containerRef}
            onClick={() => inputRef.current?.focus()}
            className={styles.otpBoxContainer}
          >
            {[...Array(6)].map((_, index) => (
              <div
                key={index}
                className={cx(styles.otpBox, {
                  [styles.otpBoxSelected]: index === otp.length,
                })}
              >
                {otp[index] || ''}
              </div>
            ))}
          </div>
          <div className={styles.resendContainer}>
            <div className={styles.error}>{error}</div>
            {timer > 0 ? (
              <div className={styles.resendTimer}>
                Request another OTP in 00:{String(timer).padStart(2, '0')}
              </div>
            ) : (
              <div onClick={handleResend} className={styles.resendCta}>
                Resend OTP
              </div>
            )}
          </div>
        </div>
        <Button
          emphasis="high"
          label="Proceed"
          onClick={handleProceedClick}
          size="large"
          disabled={loader || String(otp).trim().length !== 6}
          loading={loader}
        />
      </div>
    )
  );
};
export default OtpDrawer;
