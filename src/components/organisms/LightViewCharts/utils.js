import dayjs from 'dayjs';
import {
  PERIOD_CONSTANTS,
  PRE_OPEN_SESSION_TIME,
  SESSION_TIMES,
} from './enums';

let globalHolidaysMap = {};
let globalMuhuratMap = {};

export const setGlobalMaps = (holidaysMap = {}, muhuratMap = {}) => {
  globalHolidaysMap = holidaysMap;
  globalMuhuratMap = muhuratMap;
};

const isMuhuratDay = (date) => {
  const dateObj = dayjs(date);
  const dateString = dateObj.format('YYYYMMDD');
  return !!globalMuhuratMap[dateString];
};

const isWeekend = (date) => {
  const day = dayjs(date).day();
  return day === 0 || day === 6;
};

const weekendOrHoliday = (date) => {
  let currentDate = dayjs(date);

  if (isMuhuratDay(currentDate)) {
    return currentDate;
  }

  let dateString = currentDate.format('YYYYMMDD');
  while (
    (isWeekend(currentDate) || globalHolidaysMap[dateString]) &&
    !isMuhuratDay(currentDate)
  ) {
    currentDate = currentDate.subtract(1, 'day');
    dateString = currentDate.format('YYYYMMDD');
  }
  return currentDate;
};

const addBusinessDays = (date) => {
  let newDate = date;
  while (isWeekend(newDate)) {
    newDate = newDate.add(1, 'day');
  }
  return newDate;
};
const getSessionTime = (date) => {
  const dayJs = dayjs(date);

  if (isMuhuratDay(dayJs)) {
    const muhuratSession = globalMuhuratMap[dayJs.format('YYYYMMDD')];
    if (muhuratSession) {
      const allSplittedTimings = muhuratSession.split(/[-,]/g);
      const startTime = allSplittedTimings[0];
      const endTime = allSplittedTimings[allSplittedTimings.length - 1];

      return {
        start: {
          hour: parseInt(startTime.slice(0, 2), 10),
          minutes: parseInt(startTime.slice(2), 10),
        },
        end: {
          hour: parseInt(endTime.slice(0, 2), 10),
          minutes: parseInt(endTime.slice(2), 10),
        },
        preOpen: {
          ...PRE_OPEN_SESSION_TIME,
        },
      };
    }
  }

  return {
    start: {
      ...SESSION_TIMES.start,
    },
    end: {
      ...SESSION_TIMES.end,
    },
    preOpen: {
      ...PRE_OPEN_SESSION_TIME,
    },
  };
};

const getSessionTimings = (date) => {
  const dayJs = dayjs(date);
  if (isMuhuratDay(dayJs)) {
    const muhuratSession = globalMuhuratMap[dayJs.format('YYYYMMDD')];
    if (muhuratSession) {
      return muhuratSession.split(',');
    }
  }
  return ['0915-1530']; // Default market session
};

const checkIfDateFallsBetweenSessionDurations = (
  date,
  autoCheckInAllMap = true,
  initialSessions = ['0915-1530'],
) => {
  const currentSessions = [];
  const previousSessions = [];
  const futureSessions = [];

  const sessions = autoCheckInAllMap
    ? getSessionTimings(date)
    : initialSessions;

  const copyOfDate = new Date(date);
  const startDate = new Date(date);
  const endDate = new Date(date);

  for (let i = 0; i < sessions.length; i += 1) {
    const [startTime, endTime] = sessions[i].split('-');

    startDate.setHours(
      startTime.substring(0, 2),
      startTime.substring(2, 4),
      0,
      0,
    );
    endDate.setHours(endTime.substring(0, 2), endTime.substring(2, 4), 0, 0);

    const copyOfDateTimeStamp = copyOfDate.valueOf();
    const startDateTimeStamp = startDate.valueOf();
    const endDateTimeStamp = endDate.valueOf();

    if (
      copyOfDateTimeStamp >= startDateTimeStamp &&
      copyOfDateTimeStamp < endDateTimeStamp
    ) {
      currentSessions.push([startTime, endTime]);
    } else if (copyOfDateTimeStamp > endDateTimeStamp) {
      previousSessions.push([startTime, endTime]);
    } else if (copyOfDateTimeStamp < startDateTimeStamp) {
      futureSessions.push([startTime, endTime]);
    }
  }

  return {
    currentSessions,
    previousSessions,
    futureSessions,
  };
};

const periodRangeCalculator = (period) => {
  const currentDate = dayjs();
  const multiplier = PERIOD_CONSTANTS.firstValueInRangeDays[period]
    ? PERIOD_CONSTANTS.firstValueInRangeDays[period] - 1
    : PERIOD_CONSTANTS.firstValueInRangeDays[period];

  const previousDate = currentDate.subtract(multiplier, 'day');

  return {
    startDate: currentDate.format('YYYY-MM-DD'),
    endDate: previousDate.format('YYYY-MM-DD'),
  };
};

const isWithinSession = (timestamp) => {
  if (!timestamp.isValid()) return false;

  const sessionData = getSessionTime(timestamp);
  const sessionStart = dayjs(timestamp)
    .hour(sessionData.start.hour)
    .minute(sessionData.start.minutes)
    .second(0)
    .millisecond(0);

  const sessionEnd = dayjs(timestamp)
    .hour(sessionData.end.hour)
    .minute(sessionData.end.minutes)
    .second(0)
    .millisecond(0);

  const currentTime = dayjs(timestamp).second(0).millisecond(0);
  return (
    currentTime.valueOf() >= sessionStart.valueOf() &&
    currentTime.valueOf() < sessionEnd.valueOf()
  );
};

const isPreOpen = (timestamp) => {
  if (!timestamp.isValid()) return false;

  const sessionData = getSessionTime(timestamp);
  const preOpen = dayjs(timestamp)
    .hour(sessionData.preOpen.hour)
    .minute(sessionData.preOpen.minutes)
    .second(0)
    .millisecond(0);

  const sessionStart = dayjs(timestamp)
    .hour(sessionData.start.hour)
    .minute(sessionData.start.minutes)
    .second(0)
    .millisecond(0);

  const currentTime = dayjs(timestamp).second(0).millisecond(0);

  return (
    currentTime.valueOf() >= preOpen.valueOf() &&
    currentTime.valueOf() < sessionStart.valueOf()
  );
};

const isPreMarket = (timestamp) => {
  if (!timestamp.isValid()) return false;

  const sessionData = getSessionTime(timestamp);
  const hour = timestamp.hour();
  const minute = timestamp.minute();

  return (
    hour < sessionData.start.hour ||
    (hour === sessionData.start.hour && minute < sessionData.start.minutes)
  );
};

const dayAggregation = (dataSet) => {
  const data = dataSet.map((dt) => {
    const date = dayjs(dt[0], 'DD-MM-YYYY HH:mm');
    return { value: dt[4], time: date.valueOf() };
  });
  return data;
};

const minuteAggregation = (data, range) => {
  if (!data || !Array.isArray(data)) {
    return [];
  }

  const multiplier = PERIOD_CONSTANTS.range[range].periodicity.period; // minutes
  const multiplierMs = multiplier * 60 * 1000; // convert to milliseconds
  const transformedData = [];

  let nextDataBarTs = null;
  let prevDataBar = null;

  data.forEach((item) => {
    const [dateTimeStr, , , , close] = item;
    const timestamp = dayjs(dateTimeStr, 'DD-MM-YYYY HH:mm');
    const currentTs = timestamp.valueOf();

    if (!timestamp.isValid()) {
      return;
    }

    if (isWithinSession(timestamp)) {
      if (nextDataBarTs) {
        if (
          timestamp.hour() === SESSION_TIMES.end.hour &&
          timestamp.minute() === SESSION_TIMES.end.minutes
        ) {
          if (prevDataBar) {
            transformedData.push({ ...prevDataBar });
          }
          prevDataBar = null;
        } else if (currentTs < nextDataBarTs) {
          // Update value of current bar
          prevDataBar.value = Number(close);
        } else {
          // Push current bar and create new one
          if (prevDataBar) {
            transformedData.push({ ...prevDataBar });
          }

          // Calculate new bar time
          const timeSinceStart = currentTs - prevDataBar.time * 1000;
          const barsToAdd = Math.floor(timeSinceStart / multiplierMs);
          const newBarTs = prevDataBar.time * 1000 + barsToAdd * multiplierMs;

          prevDataBar = {
            time: newBarTs / 1000, // Store as seconds
            value: Number(close),
          };
          nextDataBarTs = newBarTs + multiplierMs;
        }
      } else if (
        (timestamp.minute() === SESSION_TIMES.start.minutes &&
          timestamp.hour() === SESSION_TIMES.start.hour) ||
        timestamp.minute() % multiplier === 0 ||
        (range === '1w' &&
          (timestamp.minute() === 15 || timestamp.minute() === 45))
      ) {
        prevDataBar = {
          time: currentTs / 1000, // Store as seconds
          value: Number(close),
        };
        nextDataBarTs = currentTs + multiplierMs;
      }
    } else {
      // Outside trading hours
      if (prevDataBar) {
        transformedData.push({ ...prevDataBar });
      }
      prevDataBar = null;
      nextDataBarTs = null;
    }
  });
  // Push final bar if within session
  if (prevDataBar && isWithinSession(dayjs(prevDataBar.time * 1000))) {
    transformedData.push({ ...prevDataBar });
  }

  return transformedData;
};

const addMinutes = (date, minutes) => dayjs(date).add(minutes, 'minute');

const subMinutes = (date, minutes) => dayjs(date).subtract(minutes, 'minute');

const isPostSession = (timestamp) => {
  if (!timestamp.isValid()) return false;

  const sessionData = getSessionTime(timestamp);
  const hour = timestamp.hour();
  const minute = timestamp.minute();
  return (
    hour > sessionData.end.hour ||
    (hour === sessionData.end.hour && minute >= sessionData.end.minutes)
  );
};

const intradayAggregation = (data, range) => {
  if (!data || !Array.isArray(data)) return [];

  const multiplier = PERIOD_CONSTANTS.range[range].periodicity.period;
  const transformedData = minuteAggregation(data, range);
  const whiteSpaceData = [];

  if (transformedData.length > 0) {
    // Handle existing data case
    let lastAvailableData = dayjs(
      transformedData[transformedData.length - 1].time * 1000,
    );

    while (isWithinSession(lastAvailableData)) {
      const whiteSpace = {
        time: addMinutes(lastAvailableData, multiplier).valueOf() / 1000,
      };
      whiteSpaceData.push(whiteSpace);
      lastAvailableData = dayjs(
        whiteSpaceData[whiteSpaceData.length - 1].time * 1000,
      );
    }

    const currentTime = weekendOrHoliday(dayjs());
    const sessionInfo = checkIfDateFallsBetweenSessionDurations(
      currentTime.toDate(),
    );
    // Handle post market session
    if (
      isPostSession(currentTime) &&
      sessionInfo.currentSessions.length === 0
    ) {
      const dataPoint = {
        time:
          dayjs()
            .hour(
              parseInt(
                sessionInfo.previousSessions?.[
                  sessionInfo.previousSessions.length - 1
                ]?.[1]?.slice(0, 2) || 0,
                10,
              ),
            )
            .minute(
              parseInt(
                sessionInfo.previousSessions?.[
                  sessionInfo.previousSessions.length - 1
                ]?.[1]?.slice(2) || 0,
                10,
              ),
            )
            .second(0)
            .valueOf() / 1000,
        value: Number(data[data.length - 1][4]),
      };
      transformedData.push(dataPoint);
      return {
        data: transformedData,
        ind: transformedData.length - 1,
      };
    }
  } else {
    // Handle no data case during market hours
    const currentTime = dayjs();
    if (isWithinSession(currentTime)) {
      const reduceMinutesBy = currentTime.minute() % multiplier;
      const lastAvailableData = subMinutes(currentTime, reduceMinutesBy)
        .second(0)
        .millisecond(0);

      transformedData.push({
        time: lastAvailableData.valueOf() / 1000,
      });

      let nextTime = addMinutes(lastAvailableData, multiplier);

      while (isWithinSession(nextTime)) {
        const whiteSpace = {
          time: nextTime.valueOf() / 1000,
        };
        whiteSpaceData.push(whiteSpace);
        nextTime = addMinutes(nextTime, multiplier);
      }
    }
  }

  return {
    data: [...transformedData, ...whiteSpaceData],
    ind: transformedData.length - 1,
  };
};

const weekMonthAggregation = (dataSet, isWeek = false) => {
  if (!dataSet || !Array.isArray(dataSet)) {
    return [];
  }

  const data = [];
  let eachDataPt = {};
  let prevDate = null;

  dataSet.forEach((d, i) => {
    const date = dayjs(d[0], 'DD-MM-YYYY HH:mm');
    if (!date.isValid()) {
      return;
    }
    if (
      i !== 0 &&
      (isWeek ? date.isSame(prevDate, 'week') : date.isSame(prevDate, 'month'))
    ) {
      // Update value for same week/month
      eachDataPt.value = Number(d[4]);
    } else {
      // Push previous data point and start new one
      if (i !== 0) {
        data.push({ ...eachDataPt });
      }
      eachDataPt = {
        time: date.valueOf() / 1000,
        value: Number(d[4]),
      };
      prevDate = date;
    }
  });

  // Push the last data point
  if (eachDataPt.time) {
    data.push({ ...eachDataPt });
  }

  return data;
};

const transformData = (data, range) => {
  if (!data || !Array.isArray(data)) return [];

  if (range === '1d') {
    return intradayAggregation(data, range);
  }
  if (range === '1w') {
    return minuteAggregation(data, range);
  }
  if (range === '1m' || range === '3m') {
    return dayAggregation(data);
  }
  if (range === '1y') {
    return weekMonthAggregation(data, true);
  }
  if (range === '3y' || range === '5y') {
    return weekMonthAggregation(data);
  }
  return minuteAggregation(data, '1w');
};

const getColorForChart = (lastValue, firstValue, theme) => {
  if (lastValue - firstValue === 0) {
    return {
      color: '#A8A8A8',
      shade: 'rgba(240, 240, 240, 0.1)',
    };
  }

  if (lastValue - firstValue < 0) {
    return {
      color: theme === 'dark' ? '#d23d50' : '#d23d50',
      shade:
        theme === 'dark' ? 'rgba(79, 39, 44,0.01)' : 'rgba(253, 242, 243,0.01)',
    };
  }
  return {
    color: theme === 'dark' ? '#02A85D' : '#21C179',
    shade:
      theme === 'dark'
        ? 'rgba(50, 77, 31,0.001)'
        : 'rgba(205, 240, 216, 0.00001)',
  };
};

const getSeriesColor = (data, range, pc, theme = 'light', intradayInd = 0) => {
  if (!data || data.length === 0) return {};

  let firstValue = data[0].value;
  let lastValue = data[data.length - 1].value;

  // Special handling for 1d range
  if (range.toLowerCase() === '1d') {
    lastValue = data[intradayInd].value;
    const currentTime = dayjs();

    if (isPreMarket(currentTime) && !isPreOpen(currentTime)) {
      firstValue = data[0].value;
    } else {
      // From pre-open session till post market session, use previous close
      firstValue = pc || data[0]?.value;
    }
  }

  // Calculate colors based on value difference
  return getColorForChart(lastValue, firstValue, theme);
};

const updateChartColor = (data, tickValue, range, pc, theme = 'light') => {
  if (!data || data.length === 0) return {};

  let firstValue = data[0]?.value;
  // Handle 1d range specially
  if (range === '1d') {
    const currentTime = dayjs();

    if (isPreOpen(currentTime)) {
      firstValue = pc;
    } else if (
      isPreMarket(currentTime) &&
      !isPreOpen(currentTime) &&
      !isWithinSession(currentTime)
    ) {
      firstValue = data[0]?.value;
    } else {
      firstValue = pc || data[0]?.value;
    }
  }

  if (!firstValue) return {};

  return getColorForChart(tickValue, firstValue, theme);
};

const updateLastTradedPrice = ({
  data,
  tickValue,
  range,
  chartRef,
  pClose,
  theme,
  chartSeries,
}) => {
  // to do: add muhurat handling
  if (!data || !range || !chartRef) return null;
  const currentTime = dayjs();
  const weekend = currentTime.day() === 0 || currentTime.day() === 6;
  const isMuhurat = isMuhuratDay(currentTime.toDate());

  // Create a shallow copy of data array
  const updatedData = [...data];

  if (isWithinSession(currentTime) && (isMuhurat || !weekend)) {
    if (range === '1y') {
      const weekStartTime = Math.floor(
        currentTime.startOf('week').add(1, 'day').valueOf() / 1000
      );
      const lastPoint = updatedData[updatedData.length - 1];
      if (lastPoint && lastPoint.time === weekStartTime) {
        lastPoint.value = tickValue;
      } else {
        updatedData.push({
          time: weekStartTime,
          value: tickValue,
        });
      }
    } else if (['3y', '5y'].includes(range)) {
      let startDayOfMonth = currentTime.date(1).startOf('day');

      if (isWeekend(startDayOfMonth)) {
        startDayOfMonth = addBusinessDays(startDayOfMonth);
      }

      // Update or add the data point
      const monthStartTimestamp = Math.floor(startDayOfMonth.valueOf() / 1000);
      const lastPoint = updatedData[updatedData.length - 1];
      const lastPointDate = dayjs(lastPoint.time * 1000);
      let lastPointStartDate = lastPointDate.date(1).startOf('day');
      // Apply same weekend adjustment to last point
      if (isWeekend(lastPointStartDate)) {
        lastPointStartDate = addBusinessDays(lastPointStartDate);
      }

      const lastPointTimeStamp = Math.floor(
        lastPointStartDate.valueOf() / 1000,
      );
      if (lastPointTimeStamp === monthStartTimestamp) {
        // Update existing point for this month
        lastPoint.value = tickValue;
      } else {
        // Add new point for this month
        updatedData.push({
          time: monthStartTimestamp,
          value: tickValue,
        });
      }
    } else if (range === '1d') {
      const multiplier = PERIOD_CONSTANTS.range[range].periodicity.period;

      // Round current time to nearest multiplier interval
      const currentMinute = currentTime.minute();
      const roundedMinutes =
        Math.floor(currentMinute / multiplier) * multiplier;
      const roundedTime = currentTime
        .minute(roundedMinutes)
        .second(0)
        .millisecond(0);
      const roundedTimestamp = roundedTime.valueOf() / 1000;
      // Find the point for current time period
      const currentPeriodIndex = updatedData.findIndex(
        (point) => point.time === roundedTimestamp,
      );

      if (currentPeriodIndex === -1) {
        // If point doesn't exist for current period, find last valid point
        let lastValidIndex = -1;
        updatedData.forEach((point, index) => {
          if (!point.time) return;
          const sessionInfo = checkIfDateFallsBetweenSessionDurations(
            new Date(point.time * 1000),
          );

          if (
            sessionInfo.currentSessions.length > 0 ||
            sessionInfo.previousSessions.length > 0
          ) {
            if (Object.prototype.hasOwnProperty.call(point, 'value')) {
              lastValidIndex = index;
            }
          }
        });

        // Update the last valid point
        if (lastValidIndex >= 0) {
          updatedData[lastValidIndex].value = tickValue;
        }
      } else {
        // Update current period point
        updatedData[currentPeriodIndex].value = tickValue;
      }
    } else if (range === '1w') {
      const sessionInfo = checkIfDateFallsBetweenSessionDurations(
        currentTime.toDate(),
      );

      if (sessionInfo.currentSessions.length > 0) {
        const multiplier = PERIOD_CONSTANTS.range[range].periodicity.period;
        const lastPoint = updatedData[updatedData.length - 1];

        if (lastPoint) {
          const lastTickDate = dayjs(lastPoint.time * 1000);
          const nextTick = addMinutes(lastTickDate, multiplier);

          if (currentTime.valueOf() > nextTick.valueOf()) {
            // Add new point at next tick interval
            updatedData.push({
              time: Math.floor(nextTick.valueOf() / 1000),
              value: tickValue,
            });
          } else {
            // Update existing point
            lastPoint.value = tickValue;
          }
        } else {
          // If no last point exists, create new point at current time
          const currentMinute = currentTime.minute();
          const roundedMinutes =
            Math.floor(currentMinute / multiplier) * multiplier;
          const roundedTime = currentTime
            .minute(roundedMinutes)
            .second(0)
            .millisecond(0);

          updatedData.push({
            time: Math.floor(roundedTime.valueOf() / 1000),
            value: tickValue,
          });
        }
      }
    } else if (range === '1m' || range === '3m') {
      const sessionInfo = checkIfDateFallsBetweenSessionDurations(
        currentTime.toDate(),
      );
      if (sessionInfo.currentSessions.length > 0) {
        // Round to start of current day
        const currentDayStart = currentTime.startOf('day');
        const lastPoint = updatedData[updatedData.length - 1];
        // If last point is from a different day, add new point
        if (
          lastPoint &&
          dayjs(lastPoint.time).startOf('day').valueOf() <
            currentDayStart.valueOf()
        ) {
          updatedData.push({
            time: currentDayStart.valueOf(),
            value: tickValue,
          });
        } else if (lastPoint) {
          // Update existing point for today
          lastPoint.value = tickValue;
        }
      }
    }
  } else if (isPostSession(currentTime)) {
    // Post market update
    const sessionEndTime = dayjs()
      .hour(SESSION_TIMES.end.hour)
      .minute(SESSION_TIMES.end.minutes)
      .second(0);

    const lastPoint = updatedData[updatedData.length - 1];
    if (
      lastPoint &&
      lastPoint.time >= Math.floor(sessionEndTime.valueOf() / 1000)
    ) {
      lastPoint.value = tickValue;
    } else {
      updatedData.push({
        time: Math.floor(sessionEndTime.valueOf() / 1000),
        value: tickValue,
      });
    }
  }

  const colors = updateChartColor(updatedData, tickValue, range, pClose, theme);
  chartSeries.setData(updatedData);
  return { data: updatedData, colors };
};

const setMarketTimer = (time, callback) => {
  if (time > 0) {
    return setTimeout(() => {
      if (isWithinSession(dayjs())) {
        callback();
      }
    }, time);
  }

  return null;
};

const calculateStartDate = (rangeSpan, callback) => {
  let startDate = dayjs();
  let newSession = true;
  let timerId = null; // Add variable to store timer ID

  if (rangeSpan.toLowerCase() === '1d' && isPreMarket(startDate)) {
    // Get previous day and handle weekend/holiday
    const sessionData = getSessionTime(startDate);
    const previousDay = startDate.subtract(1, 'day');
    startDate = weekendOrHoliday(previousDay).format('YYYY-MM-DD');
    newSession = false;
    const marketOpenTime = dayjs(startDate)
      .hour(sessionData.start.hour)
      .minute(sessionData.start.minutes)
      .second(0)
      .millisecond(0);

    const timeUntilMarketOpen = marketOpenTime.valueOf() - dayjs().valueOf();
    timerId = setMarketTimer(timeUntilMarketOpen, callback);
    return {
      startDate,
      newSession,
      timerId, // Return the timer ID so it can be cleared
    };
  }

  if (rangeSpan.toLowerCase() === '1d') {
    startDate = weekendOrHoliday(startDate).format('YYYY-MM-DD');
    newSession = false;
  }

  return {
    startDate,
    newSession,
    timerId,
  };
};

export {
  periodRangeCalculator,
  transformData,
  getSeriesColor,
  updateLastTradedPrice,
  calculateStartDate,
};
