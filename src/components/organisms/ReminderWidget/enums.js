import orderRejectedLight from '../../../assets/images/order_rejected_reminder.png';
import orderRejectedDark from '../../../assets/images/order_rejected_reminder_dark.png';
import orderSuccessLight from '../../../assets/images/order_success_reminder.png';
import orderSuccessDark from '../../../assets/images/order_success_reminder_dark.png';
import orderMtfLight from '../../../assets/images/order_mtf_reminder.png';
import orderMtfDark from '../../../assets/images/order_mtf_reminder_dark.png';
import fundsAbandonLight from '../../../assets/images/add_funds_abandonment.png';
import fundsAbandonDark from '../../../assets/images/add_funds_abandonment_dark.png';
import fundsfailedLight from '../../../assets/images/add_funds_failed.png';
import fundsfailedDark from '../../../assets/images/add_funds_failed_dark.png';

export const ORDER_PAD = '/order-pad';

export const event_type = {
  order: {
    ORDER_PAD_ABANDONEMENT: 'ORDER_PAD_ABANDONEMENT',
    STOCK_SIP_ABANDONEMENT: 'STOCK_SIP_ABANDONEMENT',
    FAILED_ORDERS: 'FAILED_ORDERS',
  },
  funds: {
    ADD_FUNDS_ABANDONEMENT: 'ADD_FUNDS_ABANDONEMENT',
    FAILED_ADD_FUNDS: 'FAILED_ADD_FUNDS',
  },
};

export const BACKGROUND_IMAGES = {
  [event_type.order.FAILED_ORDERS]: {
    light: orderRejectedLight,
    dark: orderRejectedDark,
  },
  [event_type.order.ORDER_PAD_ABANDONEMENT]: {
    light: orderMtfLight,
    dark: orderMtfDark,
  },
  [event_type.funds.ADD_FUNDS_ABANDONEMENT]: {
    light: fundsAbandonLight,
    dark: fundsAbandonDark,
  },
  [event_type.funds.FAILED_ADD_FUNDS]: {
    light: fundsfailedLight,
    dark: fundsfailedDark,
  },
  default: {
    light: orderSuccessLight,
    dark: orderSuccessDark,
  },
};

export const event_actions = {
  REMINDERWIDGET_VIEW: 'reminderwidget_view',
  REMINDERWIDGET_VIEWALLPAGEVIEW: 'reminderwidget_viewallpageview',
  REMINDERWIDGET_SCROLL: 'reminderwidget_scroll',
  REMINDERWIDGET_VIEWALLPAGESCROLL: 'reminderwidget_viewallpagescroll',
  REMINDERWIDGET_CLICK: 'reminderwidget_click',
  REMINDERWIDGET_VIEWALLPAGECLICK: 'reminderwidget_viewallpageclick',
  REMINDERWIDGET_CTACLICK: 'reminderwidget_ctaclick',
  REMINDERWIDGET_DISMISS: 'reminderwidget_dismiss',
  REMINDERWIDGET_VIEWALLCLICK: 'reminderwidget_viewallclick',
};

export const SCREEN_NAME = '/homescreen';
export const VERTICAL_NAME = 'homescreen_4.0';
export const EVENT = {
  CUSTOM_EVENT: 'custom_event',
  OPEN_SCREEN: 'openScreen',
};
export const EVENT_CATEGEORY = 'homescreen';
export const OPEN_SCREEN_EVENT = 'openScreen';
