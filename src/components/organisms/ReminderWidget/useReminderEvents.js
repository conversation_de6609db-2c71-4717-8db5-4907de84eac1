import { useRef } from 'react';
import { useAnalyticsEventForWidget } from '../../../hooks/analyticsHooks';
import {
  EVENT,
  EVENT_CATEGEORY,
  SCREEN_NAME,
  VERTICAL_NAME,
  event_actions,
} from './enums';
import { log } from '../../../utils/commonUtil';
import { useUserReadiness } from '../../../query/generalQuery';

const useReminderWidgetEvents = ({ subCohortId }) => {
  const { sendAnalyticsEventWidget } = useAnalyticsEventForWidget();
  const { data, isLoading } = useUserReadiness(true);

  const userReadinessData = useRef({
    hasInvested: null,
    irStatus: null,
  });

  if (!isLoading && data) {
    userReadinessData.current = {
      hasInvested: data?.hasInvested,
      irStatus: data?.irStatus,
    };
  }

  const handleEvents = (params) => {
    const eventData = {
      screenName: params?.screenName || SCREEN_NAME,
      event: params?.event || EVENT.CUSTOM_EVENT,
      category: params?.category || EVENT_CATEGEORY,
      verticalName: params?.verticalName || VERTICAL_NAME,
      label: params?.label,
      label2: params?.label2,
      label3: params?.label3,
      label4: params?.label4 || subCohortId,
      label5: params?.label5,
      label6: `irstatus: ${userReadinessData.current.irStatus} | isinvestedstatus: ${userReadinessData.current.hasInvested}`,
      action: params?.action,
    };

    log('#### Reminder Widget Event:', eventData);
    sendAnalyticsEventWidget(eventData);
  };

  const events = useRef({
    onPageView: ({ isFullPage, label, label2, label3 }) => {
      handleEvents({
        event: EVENT.OPEN_SCREEN,
        action: isFullPage
          ? event_actions.REMINDERWIDGET_VIEWALLPAGEVIEW
          : event_actions.REMINDERWIDGET_VIEW,
        label,
        label2,
        label3,
      });
    },
    onWidgetScroll: ({ isFullPage }) => {
      handleEvents({
        action: isFullPage
          ? event_actions.REMINDERWIDGET_VIEWALLPAGESCROLL
          : event_actions.REMINDERWIDGET_SCROLL,
      });
    },
    onWidgetClick: ({ label, label2, label3, isFullPage }) => {
      handleEvents({
        action: isFullPage
          ? event_actions.REMINDERWIDGET_VIEWALLPAGECLICK
          : event_actions.REMINDERWIDGET_CLICK,
        label,
        label2,
        label3,
      });
    },
    onCtaClick: ({ label, label2, label3 }) => {
      handleEvents({
        action: event_actions.REMINDERWIDGET_CTACLICK,
        label,
        label2,
        label3,
      });
    },
    onDismiss: ({ label, label2, label3 }) => {
      handleEvents({
        action: event_actions.REMINDERWIDGET_DISMISS,
        label,
        label2,
        label3,
      });
    },
    onViewAllClick: () => {
      handleEvents({
        action: event_actions.REMINDERWIDGET_VIEWALLCLICK,
      });
    },
  });

  return events.current;
};

export { useReminderWidgetEvents };
