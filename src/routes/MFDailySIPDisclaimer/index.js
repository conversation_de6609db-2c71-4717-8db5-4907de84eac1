import { useEffect } from 'react';
import queryString from 'query-string';

import { exitApp } from '../../utils/bridgeUtils';
import DailySipDisclaimer from '../../components/molecules/DailySipDisclaimer/DailySipDisclaimer';
import { setDailySipCohort, setIsDailySipMF } from '../../utils/commonUtil';

const MFDailySIPDisclaimer = () => {
  const query = queryString.parse(window.location?.search);
  const { cohort = '', isDailySipMF = false } = query || {};

  useEffect(() => {
    document.body.style.background = 'transparent';

    setDailySipCohort(cohort);
    setIsDailySipMF(isDailySipMF);
  }, []);

  return <DailySipDisclaimer active triggerClose={exitApp} />;
};

export default MFDailySIPDisclaimer;
