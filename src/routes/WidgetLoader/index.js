import { useEffect } from 'react';

const WidgetLoader = () => {
  useEffect(() => {
    // Preload ETFCard chunk
    import(/* webpackChunkName: "ETFCard" */ '../ETFCard');

    // Preload FirstStockCard chunk
    import(/* webpackChunkName: "FirstStockCard" */ '../FirstStockPage');

    // Preload ReminderWidget chunk
    import(/* webpackChunkName: "ReminderWidget" */ '../ReminderWidget');

    // Preload ReminderWidgetList chunk
    import(
      /* webpackChunkName: "ReminderWidgetList" */ '../ReminderWidgetList'
    );

    // Preload NewsFeedWidget chunk
    import(/* webpackChunkName: "NewsFeedWidget" */ '../NewsFeedWidget');

    // Preload NewsFeedList chunk
    import(/* webpackChunkName: "NewsFeedList" */ '../NewsFeedList');

    // Preload NewsListViewAll chunk
    import(/* webpackChunkName: "NewsListViewAll" */ '../NewsListViewAll');

    // You can also add logic here to redirect or display something
  }, []);

  return <div>Widget Loader - Preloading ETF and FirstStock chunks</div>;
};

export default WidgetLoader;
