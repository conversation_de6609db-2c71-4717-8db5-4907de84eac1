import { useEffect } from 'react';
import queryString from 'query-string';

import FundSelectionPopup from '../../pages/FundSelectionPage/FundSelectionPage';
import { setDailySipCohort, setIsDailySipMF } from '../../utils/commonUtil';

const FundSelectionPopupPageRoute = () => {
  const query = queryString.parse(window.location?.search);
  const { cohort = '', isDailySipMF = false } = query || {};

  useEffect(() => {
    document.body.style.background = 'transparent';

    setDailySipCohort(cohort);
    setIsDailySipMF(isDailySipMF);
  }, []);

  return <FundSelectionPopup />;
};

export default FundSelectionPopupPageRoute;
