import { useEffect } from 'react';
import queryString from 'query-string';

import MfDailySipSelectAmount from '../../pages/MfDailySipSelectAmount';
import { exitApp } from '../../utils/bridgeUtils';
import { setDailySipCohort, setIsDailySipMF } from '../../utils/commonUtil';

const MfDailySipSelectAmountRoute = () => {
  const fund = JSON.parse(localStorage.getItem('selectedFund'));
  const query = queryString.parse(window.location?.search);
  const { cohort = '', isDailySipMF = false } = query || {};

  useEffect(() => {
    document.body.style.background = 'transparent';

    setDailySipCohort(cohort);
    setIsDailySipMF(isDailySipMF);
    return () => {
      localStorage.removeItem('selectedFund');
    };
  }, []);

  return (
    <MfDailySipSelectAmount
      fund={fund}
      showAmountSelectionPopup
      onAmountSelectionPopupClose={exitApp}
    />
  );
};

export default MfDailySipSelectAmountRoute;
