import { useEffect, useState } from 'react';
import queryString from 'query-string';

import MFDailySipLoader from '../../components/molecules/MFDailySipLoader/MFDailySipLoader';
import SipCard from '../../pages/SipCard';
import AggregatorComponent from '../../HOC/Aggregator/AggregatorComponent';
import { AGGREGATOR_API } from '../../config/urlConfig';
import CentralLoader from '../../components/atoms/CentralLoader/CentralLoader';
import {
  notifyNativeApp,
  getStartupParamsAllCallback,
} from '../../utils/bridgeUtils';
import {
  isIosBuild,
  isDarkMode,
  setIsDailySipMF,
} from '../../utils/commonUtil';

import styles from './index.scss';

const SipCardRouteWrapper = ({ pages = {} }) => <SipCard data={pages} />;

const SipCardRoute = () => {
  const [isLoading, setIsLoading] = useState(true);
  const [nativeData, setNativeData] = useState(null);
  const query = queryString.parse(window.location?.search);
  const { aggrKey, businessType } = query || {};

  const getWidgetHeight = () => {
    if (isIosBuild()) {
      return 270;
    }

    if (businessType === 'MUTUAL_FUND') {
      const { devicePixelRatio } = window;
      return (270 + 15) * devicePixelRatio;
    }

    return (270 + 15).toString();
  };

  const setFragmentHeight = () => {
    notifyNativeApp({
      flowType:
        businessType === 'MUTUAL_FUND'
          ? 'mfHomeH5FragmentHeight'
          : 'combinedHomeH5FragmentHeight',
      height: getWidgetHeight(),
      widgetId: 'mf-sip-card',
    });
  };

  const addBodyStyles = () => {
    if (isIosBuild()) {
      document.body.classList.add(styles.nativeBodyMargin);
    } else if (businessType === 'MUTUAL_FUND' && isDarkMode()) {
      document.body.classList.add(styles.mfBGDark);
    } else if (businessType === 'MUTUAL_FUND') {
      document.body.classList.add(styles.mfBGLight);
    } else {
      document.body.classList.add(styles.nativeBodyMargin);
    }
  };

  const resizeMFViewPort = () => {
    const viewPortHeight = window.visualViewport.height;
    return Math.abs(285 - viewPortHeight) > 5;
  };

  useEffect(() => {
    if (aggrKey) {
      if (
        isIosBuild() ||
        businessType !== 'MUTUAL_FUND' ||
        resizeMFViewPort()
      ) {
        setFragmentHeight();
      }

      addBodyStyles();

      if (businessType === 'MUTUAL_FUND') {
        setIsDailySipMF(true);
      }
    }
  }, [aggrKey]);

  useEffect(() => {
    getStartupParamsAllCallback((result) => {
      if (result?.nativeData) {
        setNativeData(JSON.parse(result.nativeData));
      }
      setIsLoading(false);
    }).catch(() => setIsLoading(false));
  }, []);

  if (!aggrKey) {
    return null;
  }

  if (isLoading) {
    return <MFDailySipLoader />;
  }

  if (nativeData?.popularMfs) {
    return <SipCardRouteWrapper pages={{ data: nativeData }} />;
  }

  const WrappedComponent = AggregatorComponent({
    queryProps: {
      name: 'MFSipCard',
      url:
        businessType === 'MUTUAL_FUND'
          ? AGGREGATOR_API.MF_DASHBOARD
          : AGGREGATOR_API.COMBINED_DASHBOARD,
      fallbackUrl: AGGREGATOR_API.COMBINED_DASHBOARD_FALLBACK,
      queryParams: {
        keys: aggrKey,
      },
      isMF: businessType === 'MUTUAL_FUND',
    },
    loader: <CentralLoader />,
  })(SipCardRouteWrapper);

  return <WrappedComponent />;
};

export default SipCardRoute;
