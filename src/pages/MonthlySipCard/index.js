import React, { useEffect, useRef, useState } from 'react';
import cx from 'classnames';

import Shimmer from '../../components/atoms/Shimmer';
import Button from '../../components/atoms/Button/Button';
import Icon, { ICONS_NAME } from '../../components/molecules/Icon/index';
import FundSelectionPopupMonthly from '../../components/organisms/FundSelectionMonthlyPopup/FundSelectionMonthlyPopup';
import MfDailySipSelectAmount from '../MfDailySipSelectAmount';
import DailySipDisclaimer from '../../components/molecules/DailySipDisclaimer/DailySipDisclaimer';

import { useMonthlySIPAnalyticsEvents } from '../../hooks/useMonthlySIPAnalyticsEvents';
import { withErrorBoundary } from '../../HOC/WidgetErrorBoundary';
import { useDrawer } from '../../components/molecules/Drawer/Drawer';
import { formatPrice } from '../../components/molecules/Prices';
import { ATTRIBUTES, TEXT_DATA } from './utils';
import { isPaytmMoney } from '../../utils/coreUtil';
import {
  amountSelectionDeeplink,
  dailySipDisclaimerDeeplink,
  fundSelectionDeeplink,
} from '../../utils/constants';
import {
  openDeepLinkPaytm,
  openDeepLinkPaytmMoney,
} from '../../utils/bridgeUtils';
import {
  isDarkMode,
  setDailySipCohort,
  getDailySipCohort,
  getIsDailySipMF,
  isPaytm,
  isIosBuild,
} from '../../utils/commonUtil';
import { PULSE_STATICS } from './enums';

import mutualFundSahiHaiImage from '../../assets/images/mutual-fund-sahi-hai-logo.svg';
import mutualFundSahiHaiImageDark from '../../assets/images/mutual-fund-sahi-hai-logo-dark.svg';

import styles from './index.scss';
import {
  enrichArrayObjectsWithLookupData,
  getObjectInArray,
} from '../../utils/array';

let timerId;
const THREE_YEARS = '3y';
const allowedRanges = new Set(['3y', '1y', '6m', '3m', '1m']);
const formatReturns = (returns) => {
  if (!returns?.length)
    return (
      <p className={styles.fundReturns}>
        <span>Abs. Returns</span>
        <span>N/A</span>
      </p>
    );
  for (let i = returns.length - 1; i >= 0; i -= 1) {
    if (allowedRanges.has(returns[i].name) && returns[i]?.percentage > 0) {
      return returns[i];
    }
  }
  return returns[0];
};

const SipCard = ({ showAnimation, transitionTime, mf, showPopupHandler }) => {
  const loaderRef = useRef();
  const { absoluteReturns } = mf.fundReturns;
  const threeYearReturn = absoluteReturns.findIndex(
    (ar) => ar.name === THREE_YEARS,
  );
  const threeYearOrLessReturn = formatReturns(absoluteReturns);
  // threeYearReturn !== -1
  //   ? absoluteReturns[threeYearReturn]
  //   : absoluteReturns[absoluteReturns.length - 1];
  const [internalShowAnimation, setInternalShowAnimation] = useState(false);

  useEffect(() => {
    setInternalShowAnimation(false);
    setTimeout(() => {
      setInternalShowAnimation(showAnimation);
    }, 0);
  }, [showAnimation, mf]);

  return (
    <div className={styles.cardRoot}>
      <div
        className={styles.sipTitle}
        onClick={() => {
          showPopupHandler(true);
        }}
      >
        <div className={styles.cardTextDetailsContainer}>
          <Icon url={mf.amcLogo} width={32} />
          <div className={styles.sipDetails} style={{ gap: 2 }}>
            <div className={styles.sipName}>{mf.schemeName}</div>
            <div className={styles.sipReturnsData}>
              <span className={styles.sipReturnsLabel}>
                {`${threeYearOrLessReturn.name.toUpperCase()}`} Returns:
              </span>
              <span
                className={`${styles.sipReturnsPercentage} ${threeYearOrLessReturn.percentage < 0 ? styles.negative : styles.positive}`}
              >
                {formatPrice(threeYearOrLessReturn.percentage, 2, false, false)}
                %
              </span>
            </div>
          </div>
          <div className={styles.dropDownIcon}>
            <Icon name={ICONS_NAME.BLACK_ARROW_DOWN} width="24px" />
          </div>
        </div>
        {showAnimation && internalShowAnimation && (
          <div className={styles.animatioBarContainer}>
            <div
              ref={loaderRef}
              className={cx(styles.sipLoader, {
                [styles.sipLoaderDark]: isDarkMode(),
              })}
              style={{ animationDuration: `${transitionTime - 0.1}s` }}
            />
          </div>
        )}
      </div>

      <span className={styles.sipWealthPlanText}>{mf.socialProofing}</span>
    </div>
  );
};

const StartYourMonthlySip = ({
  mfData: {
    popularMfs: data = [],
    subTitle,
    disclaimerText,
    cta,
    transitionTime = 10 /* ...widgetConfig */,
  },
  userSelectedFund,
  showAmountSelectionPopup,
  showFundSelectionPopup,
}) => {
  const cancelInterval = useRef(null);
  const sipDataIndex = useRef(0);
  const [sipCardData, setSipCardData] = useState(
    userSelectedFund || data?.[sipDataIndex.current] || {},
  );

  // const { sendAnalyticsEventMonthlySIP } = useMonthlySIPAnalyticsEvents();

  const {
    isOpen: isDisclaimerOpen,
    onOpen: onDisclaimerOpen,
    onClose: onDisclaimerClose,
  } = useDrawer();

  const [showAnimation, setShowAnimation] = useState(
    !(transitionTime === 0 || data.length < 2) && !userSelectedFund,
  );

  useEffect(() => {
    clearInterval(cancelInterval.current);
    cancelInterval.current = null;
    if (userSelectedFund) {
      setShowAnimation(false);
      setSipCardData(userSelectedFund);
      clearInterval(cancelInterval.current);
    }
    if (!showAnimation) return;

    cancelInterval.current = setInterval(() => {
      sipDataIndex.current = (sipDataIndex.current + 1) % data.length;
      setSipCardData(userSelectedFund || data?.[sipDataIndex.current] || {});
    }, transitionTime * 1000);

    return () => {
      clearInterval(cancelInterval.current);
      cancelInterval.current = null;
    };
  });

  const handleShowFundSelectionPopup = () => {
    setShowAnimation(false);
    showFundSelectionPopup(
      userSelectedFund || data?.[sipDataIndex.current] || {},
    );
  };

  const onDisclaimerClick = () => {
    if (isPaytmMoney()) {
      const deeplink = `${dailySipDisclaimerDeeplink}${getDailySipCohort() ? `&cohort=${getDailySipCohort()}` : ''}${getIsDailySipMF() ? '&isDailySipMF=true' : ''}`;
      openDeepLinkPaytmMoney(deeplink);
    } else {
      onDisclaimerOpen();
    }

    // sendAnalyticsEventMonthlySIP({
    //   action: PULSE_STATICS.ACTION.ON_DISCLAIMER_CLICK,
    // });
  };

  return (
    <div
      className={cx(styles.root, {
        [styles.darkModeRoot]: isDarkMode(),
      })}
    >
      <div className={styles.header}>
        {/* <Lottie
          style={{ position: 'absolute', top: '0', left: '0', width: '76px', height: '80px' }}
          className={styles.leftLottie}
          animationData={isDarkMode() ? bgLeftLottieDark : bgLeftLottie}
          autoplay
          loop
        /> */}
        <div className={styles.monthlySipContainer}>
          <img
            src={
              isDarkMode() ? mutualFundSahiHaiImageDark : mutualFundSahiHaiImage
            }
            alt={TEXT_DATA.MUTUAL_FUND_SAHI_HAI}
            className={styles.sahiHaiLogo}
          />
          <p className={styles.monthlyMfSipSubText}>{subTitle}</p>
        </div>
        {/* <Lottie
          animationData={isDarkMode() ? headerLottieDark : headerLottie}
          autoplay
          loop={false}
        /> */}
        {/* <Lottie
          style={{ position: 'absolute', top: '0', right: '0', width: '76px', height: '80px' }}
          animationData={isDarkMode() ? bgRightLottieDark : bgRightLottie}
          autoplay
          loop
        /> */}
      </div>
      {sipCardData?.schemeName ? (
        <SipCard
          mf={sipCardData}
          showPopupHandler={handleShowFundSelectionPopup}
          showAnimation={showAnimation}
          transitionTime={transitionTime}
        />
      ) : (
        <Shimmer height="50px" width="100%" />
      )}

      {/* <Lottie
        animationData={
          isDarkMode()
            ? lowToHighPriceArrowLottieDark
            : lowToHighPriceArrowLottie
        }
        autoplay
        loop={false}
        height="124px"
        width="344px"
      /> */}

      <div className={styles.footer}>
        <Button
          isPrimary
          className={styles.startSipBtn}
          style={{ backgroundColor: '#101010' }}
          buttonText={cta?.cta}
          buttonTextClassName={styles.startSipBtnText}
          onClickHandler={() => showAmountSelectionPopup(sipCardData)}
        />

        <div className={styles.footerTextContainer} style={{ gap: 2 }}>
          <div className={styles.sipDisclaimerNote}>
            {sipCardData?.cagrText},
          </div>

          <Button
            buttonText={disclaimerText}
            className={styles.readDisclaimerBtn}
            buttonTextClassName={styles.readDisclaimerBtnText}
            onClickHandler={onDisclaimerClick}
          />
        </div>
      </div>
      <DailySipDisclaimer
        active={isDisclaimerOpen}
        triggerClose={onDisclaimerClose}
      />
    </div>
  );
};

const StartYourMonthlySipWrapper = ({
  data: { data, ...fragmentConfig },
  ...rest
}) => {
  const parseData = (rawData) => {
    const widget = rawData?.widget;
    const attributes = widget?.attributes;

    function getAttributes(names) {
      return names.reduce(
        (acc, { targetName, attributeName, defaultValue }) => {
          acc[targetName] =
            getObjectInArray(attributes, 'name', attributeName)?.value ??
            defaultValue;
          return acc;
        },
        {},
      );
    }
    const {
      popularMfs,
      subTitle,
      socialProofings,
      disclaimerText,
      cagrs,
      cta,
    } = getAttributes(ATTRIBUTES);
    const enrichments = [
      {
        source: socialProofings,
        targetKey: 'socialProofing',
        extractor: (obj) => obj?.value,
        comparator: (obj, item) => item?.isin === obj?.name,
      },
      {
        source: cagrs,
        targetKey: 'cagrText',
        extractor: (obj) => obj?.value ?? obj?.cagrValue,
        comparator: (obj, item) =>
          item?.isin === obj?.name || item?.isin === obj?.isin,
      },
    ];

    return {
      popularMfs: enrichArrayObjectsWithLookupData(popularMfs, enrichments),
      subTitle,
      disclaimerText,
      subCohortId: rawData?.meta?.subCohortId,
      cta,
    };
  };

  const mfData = parseData(data);
  console.log('now/ mfData: ', mfData);
  console.log('now/ data: ', data);
  const [userSelectedFund, setUserSelectedFund] = useState(null);
  const [preSelectedFund, setPreselectedFund] = useState(null);
  const sipCardRef = useRef(null);

  const { sendAnalyticsEventMonthlySIP } = useMonthlySIPAnalyticsEvents();

  const {
    isOpen: showAmountSelectionPopup,
    onOpen: onAmountSelectionPopupOpen,
    onClose: onAmountSelectionPopupClose,
  } = useDrawer();

  const {
    isOpen: showFundSelectionPopup,
    onOpen: onFundSelectionPopupOpen,
    onClose: onFundSelectionPopupClose,
  } = useDrawer();

  const handleStorageChange = (event) => {
    if (event.key === 'selectedFund') {
      setUserSelectedFund(JSON.parse(event.newValue));
    }
  };

  useEffect(() => {
    if (mfData?.popularMfs) {
      const { subCohortId = '' } = mfData;
      setDailySipCohort(subCohortId);

      if (isPaytmMoney()) {
        window.addEventListener('storage', handleStorageChange);
      }
    }

    return () => {
      window.removeEventListener('storage', handleStorageChange);
      clearTimeout(timerId);
    };
  }, [mfData, fragmentConfig]);

  useEffect(() => {
    if (!isPaytmMoney()) {
      sendAnalyticsEventMonthlySIP({
        action: PULSE_STATICS.ACTION.PAGE_LOAD,
        event: PULSE_STATICS.OPEN_SCREEN_EVENT,
      });
    }
  }, []);

  const getDeepLink = (app, isin) => {
    const { businessId, businessType, cohortId, subCohortId } =
      data?.meta ?? {};
    const buisnessParams = `&aggrKey=${businessId}&businessType=${businessType}`;
    const isinParams = `${isin ? `&fundisin=${isin}` : ''}`;
    const cohortParams = `&cohortId=${cohortId}&subCohortId=${subCohortId}`;

    const buildParams = () => `${buisnessParams}${isinParams}${cohortParams}`;
    let deepLink =
      app === 'mini' ? mfData?.cta?.deeplinkMini : mfData?.cta?.deeplink;
    let [deeplinkStart, paramsData] = deepLink.split(
      app === 'mini' ? 'data=' : '?',
    );
    if (app === 'mini') {
      const paramsObj = JSON.parse(atob(paramsData));
      paramsObj.params = `${paramsObj.params}${buildParams()}&origin=PAYTM&os=${isIosBuild() ? 'ios' : 'android'}`;
      console.log(paramsObj.params);
      return `${deeplinkStart}data=${btoa(JSON.stringify(paramsObj))}`;
    } else {
      paramsData = `?${paramsData}${buildParams()}&origin=PAYTMMONEY&os=${isIosBuild() ? 'ios' : 'android'}`;
      console.log(paramsData);
      return `${deeplinkStart}${paramsData}`;
    }
  };

  const showFundSelectionPopupHandler = (fund) => {
    if (isPaytmMoney()) {
      localStorage.setItem('preSelectedFund', JSON.stringify(fund));
      openDeepLinkPaytmMoney(getDeepLink('native'));
    } else if (isPaytm()) {
      openDeepLinkPaytm(getDeepLink('mini'));
    } else {
      setPreselectedFund(fund);
      onFundSelectionPopupOpen();
    }
    sendAnalyticsEventMonthlySIP({
      action: PULSE_STATICS.ACTION.ON_FUND_DROPDOWN_CLICK,
    });
  };

  const showAmountSelectionPopupHandler = (fund) => {
    if (isPaytmMoney()) {
      localStorage.setItem('selectedFund', JSON.stringify(fund));
      openDeepLinkPaytmMoney(getDeepLink('native', fund.isin));
    } else if (isPaytm()) {
      openDeepLinkPaytm(getDeepLink('mini', fund.isin));
    } else {
      setUserSelectedFund(fund);
      onAmountSelectionPopupOpen();
    }

    sendAnalyticsEventMonthlySIP({
      action: PULSE_STATICS.ACTION.START_DAILY_SIP_CLICK,
    });
  };

  return (
    <div ref={sipCardRef} className={styles.sipCardContainer}>
      <StartYourMonthlySip
        mfData={mfData}
        userSelectedFund={userSelectedFund}
        fragmentConfig={{ ...fragmentConfig, ...rest }}
        showAmountSelectionPopup={showAmountSelectionPopupHandler}
        showFundSelectionPopup={showFundSelectionPopupHandler}
      />
      <FundSelectionPopupMonthly
        isOpen={showFundSelectionPopup}
        setFund={setUserSelectedFund}
        preSelectedFund={preSelectedFund}
        onClose={onFundSelectionPopupClose}
      />
      <MfDailySipSelectAmount
        fund={userSelectedFund}
        showAmountSelectionPopup={showAmountSelectionPopup}
        onAmountSelectionPopupClose={onAmountSelectionPopupClose}
      />
    </div>
  );
};

export default React.memo(withErrorBoundary(StartYourMonthlySipWrapper));
