@import '/src/commonStyles/variables.scss';

@import '/src/utils/lightVars';
@import '/src/utils/darkVars';

:root {
  @include lightVars;
}

.darkModeRoot {
  @include darkVars;
}

body {
  margin: 0;
  background: transparent !important;
}

.sipCardContainer {
  border-radius: 16px;
}

.root {
  display: flex;
  flex-direction: column;
  align-items: center;
  border-radius: 16px;
  background: linear-gradient(
    180deg,
    var(--primaryOffsetVariant) 0%,
    var(--gradientPurpleLight) 100%
  );
  padding-bottom: 8px;
}

// .darkModeRoot {
//    background: linear-gradient(180deg, #1c1157 0%, #001128 20%, #202020 100%);
// }

.header {
  display: flex;
  position: relative;
  align-self: stretch;
  align-items: center;
  justify-content: center;
}

.footer {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 100%;

  .startSipBtn {
    position: relative;
    border-radius: 48px;
    background: var(--primary);
    overflow: hidden;
    //z-index: 1;
    width: unset;
    margin: 0 12px;
    align-self: stretch;
    margin-bottom: 8px;
    margin-top: 24px;
    overflow: hidden;
    color: var(--silver900);
    text-align: center;
    text-overflow: ellipsis;
    font-family: Inter;
    font-size: 16px;
    font-style: normal;
    font-weight: 600;
    line-height: 22px;
    letter-spacing: 0.01px;

    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: linear-gradient(
        110deg,
        var(--primary) 10%,
        var(--silver100) 12%,
        var(--silver400) 14%,
        var(--silver100) 16%,
        var(--primary) 18%
      );
      background-size: 200% 100%;
      animation: shine 5s infinite linear;
      z-index: 0;
      pointer-events: none;
    }

    .startSipBtnText {
      font-weight: 500;
      position: relative;
    }
  }

  .sipDisclaimerNote {
    color: var(--grey500);
    text-align: center;
    font-family: Inter;
    font-size: 8px;
    font-style: normal;
    font-weight: 400;
    line-height: 12px;
    letter-spacing: 0.02px;
    white-space: nowrap;
  }

  .readDisclaimerBtn {
    background-color: transparent;
    padding: 0px;
    height: fit-content;
    border-radius: 48px;

    .readDisclaimerBtnText {
      color: var(--link, #1576db);
      font-family: Inter;
      font-size: 8px;
      font-style: normal;
      font-weight: 500;
      line-height: 12px;
      letter-spacing: 0.02px;
    }
  }
}

.footerTextContainer {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 2px;
}

.cardRoot {
  display: flex;
  flex-direction: column;
  background-color: var(--primaryOffsetVariant);
  border-radius: 12px;
  margin: 0 12px;
  box-shadow: 0px 0px 8px 2px #00000014;
}

.sipWealthPlanText {
  padding: 6px 12px;
  text-overflow: ellipsis;
  white-space: nowrap;
  overflow: hidden;
  color: var(--grey600);
  font-family: Inter;
  font-size: 10px;
  font-style: normal;
  font-weight: 400;
  line-height: 16px;
}

.finalWealthAmtText {
  display: inline;
  color: map-get($colors, var(--text-neutral-strong));
}

.monthlySipContainer {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 16px;

  > p {
    margin: 0px;
  }
}

// .mutualFundTitleContainer {
//   display: flex;
//   align-items:center;
//   gap: 8px;
// }

// .mutualFundTitle {
//   @include typography(heading5B, var(--text-neutral-strong));
// }

.monthlyMfSipSubText {
  @include typography(body2, #101010b2);
  color: var(--grey600);
  text-align: center;
  font-family: Inter;
  font-size: 10px;
  font-style: normal;
  font-weight: 400;
  line-height: 16px;
}

.sipTitle {
  align-self: stretch;
  position: relative;
  overflow: hidden;
  border-radius: 12px;
  background: var(--plain);
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;

  & > img {
    width: auto;
  }
}

.sipDetails {
  display: flex;
  flex-grow: 1;
  flex-direction: column;
  gap: 2px;
  height: 32px;

  .sipName {
    overflow: hidden;
    white-space: nowrap;
    width: calc(100vw - 168px);
    color: var(--grey900);
    text-overflow: ellipsis;
    font-family: Inter;
    font-size: 12px;
    font-style: normal;
    font-weight: 500;
    line-height: 16px;
  }
}

.sipReturnsData {
  display: inline-flex;
  align-items: center;
  gap: 2px;

  .sipReturnsLabel {
    font-size: 12px;
    font-weight: 400;

    @include typography(small-text1, var(--text-neutral-weak));
  }

  .mostSipTag {
    font-size: 8px;
    line-height: 10px;
    font-weight: 500;
    padding: 3px 4px;
    background: #fff6b2;
    margin-left: 8px;
  }

  .sipReturnsPercentage {
    font-size: 12px;
    font-weight: 600;
  }

  .positive {
    @include typography(small-text1A, var(--background-positive-strong));
  }

  .negative {
    @include typography(small-text1A, var(--background-negative-strong));
  }
}

.dropDownIcon {
  display: flex;
}

.sahiHaiLogo {
  width: 188px;
}

.sipLoader {
  height: 2px;
  animation: loading linear forwards;
  background: linear-gradient(90deg, #00b8f5 0%, #2f81ed 100%);
}

.cardTextDetailsContainer {
  display: flex;
  gap: 8px;
  padding: 12px 20px;
}

.sipLoaderDark {
  background: linear-gradient(90deg, #0a86bf 0%, #2361b2 100%);
}

.animationPause {
  animation-play-state: paused;
}

.animationResume {
  animation-play-state: running;
}

@keyframes loading {
  0% {
    width: 0%;
    background-size: 200% 100%;
  }
  40% {
    background-size: 200% 100%;
  }
  50% {
    background-size: 200% 100%;
  }
  75% {
    background-size: 200% 100%;
  }
  100% {
    width: 100%;
    background-size: 200% 100%;
  }
}

@keyframes shine {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}

.animatioBarContainer {
  display: flex;
  width: 100%;
  background-color: var(--grey200);
  height: 2px;
}
