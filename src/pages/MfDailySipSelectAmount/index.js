import { useState, useEffect } from 'react';

// import { Snackbar } from '@paytm-h5-common/paytm_common_ui';
import cx from 'classnames';

import OtpDrawer from '../../components/organisms/OtpDrawer/OtpDrawer';
import useAutopayCardDetails from '../../hooks/useAutopayCardDetails';
import Drawer from '../../components/molecules/Drawer/Drawer';
import { useBackPress } from '../../hooks/useNativeBackPress';
import Shimmer from '../../components/atoms/Shimmer';
import Icon, { ICONS_NAME } from '../../components/molecules/Icon';
import { useDailySIPAnalyticsEvents } from '../../hooks/useDailySIPAnalyticsEvents';

import {
  generatePaytmDeeplink,
  capitalizeFirstLetter,
  getIsDailySipMF,
} from '../../utils/commonUtil';
import {
  APPEARANCE_TYPES,
  mfH5Deeplink,
  mfH5DeeplinkPML,
} from '../../utils/constants';
import {
  openDeepLink,
  openDeepLinkPaytmMoney,
  exitApp,
} from '../../utils/bridgeUtils';
import { generateOTP } from '../../actions/dailySipAction';
import { isPaytmMoney } from '../../utils/coreUtil';
import { useSnackbar } from '../../contexts/SnackbarProvider';
import { OTP_ERROR_MESSAGES } from '../../config/mfDailySipSelectAmount';
import { PULSE_STATICS } from './enums';

import styles from './index.scss';
import {
  getMaskedEmail,
  maskMobileNumber,
} from '../../components/organisms/OtpDrawer/OtpUtils';

const MfDailySipSelectAmount = ({
  fund,
  showAmountSelectionPopup,
  onAmountSelectionPopupClose,
}) => {
  const [selectedAmount, setSelectedAmount] = useState(
    // fund?.applicableSipAmount?.[0],
    0,
  );
  const [paymentMethod, setPaymentMethod] = useState(null);
  const [isFreshUser, setIsFreshUser] = useState(false);
  // const [error, setError] = useState(null);
  const [isProcessing, setIsProcessing] = useState(false);
  const [openOtpPopup, setOpenOtpPopup] = useState(false);
  const [otpTransactionDetails, setOtpTransactionDetials] = useState(null);
  const [otpMessage, setOtpMessage] = useState('');

  const { showSnackbar } = useSnackbar();

  const { stack, pushStack } = useBackPress();
  const { sendAnalyticsEventDailySIP } = useDailySIPAnalyticsEvents();

  const {
    selectedPspApp,
    isbankAccountsListLoading,
    quickRecommendedPaymentMethods,
    selectedVpa,
    eventLabel3,
    eventLabel4,
    isMandateRegistrationFlow,
  } = useAutopayCardDetails(selectedAmount);

  useEffect(() => {
    if (showAmountSelectionPopup) {
      setSelectedAmount(fund?.applicableSipAmount?.[0]);

      // pushStack(() => {
      //   document.body.classList.remove(styles.bodyFixedPosition);
      //   setOpenOtpPopup(false);
      //   onAmountSelectionPopupClose();
      //   stack.pop();
      // });

      sendAnalyticsEventDailySIP({
        action: PULSE_STATICS.ACTION.PAGE_LOAD,
        event: PULSE_STATICS.OPEN_SCREEN_EVENT,
      });
    }
  }, [showAmountSelectionPopup]);

  useEffect(() => {
    if(openOtpPopup) {
      document.body.classList.add(styles.bodyFixedPosition);
    } else {
      document.body.classList.remove(styles.bodyFixedPosition);
    }
  }, [openOtpPopup]);

  useEffect(() => {
    setPaymentMethod(quickRecommendedPaymentMethods?.[0]);
    setIsFreshUser(isMandateRegistrationFlow);
  }, [isMandateRegistrationFlow, quickRecommendedPaymentMethods]);

  const onOTPPopupClose = () => {
    sendAnalyticsEventDailySIP({
      action: PULSE_STATICS.ACTION.ON_OTP_CLOSE,
    });

    document.body.classList.remove(styles.bodyFixedPosition);
    setOpenOtpPopup(false);
    onAmountSelectionPopupClose();
  };

  const sendPaymentEvent = (isChange) => {
    sendAnalyticsEventDailySIP({
      action: isChange
        ? PULSE_STATICS.ACTION.ON_CHANGE_CLICK
        : PULSE_STATICS.ACTION.PAY_CTA_CLICK,
      label: isFreshUser ? 'first attempt' : 'recurring attempt',
      label2: `${selectedAmount} | ${fund?.applicableSipAmount?.[0] === selectedAmount ? 'default' : 'non-default'}`,
      label3: `${paymentMethod?.upiDetails.collect?.lastUsedVpa && isPaytmMoney() ? 'UPI collect' : 'UPI Intent'} | true`,
      label5: `${
        isPaytmMoney() && paymentMethod?.upiDetails.collect?.lastUsedVpa
          ? paymentMethod?.upiDetails.collect?.lastUsedVpa
          : selectedPspApp.appName
      }`,
    });
  };

  const handlePayClick = async () => {
    sendPaymentEvent();

    setIsProcessing(true);

    try {
      const body = JSON.stringify({
        purchaseSchemeDetails: [
          {
            amount: selectedAmount,
            investmentType: 'SIP',
            isin: fund.isin,
            sipFrequency: 'DAILY',
            sipStartDate: Date.now().toString(),
            pmAmcCode: fund.pmAmcCode,
            rtaCode: fund.rtaCode,
          },
        ],
        userType: 'PML_ON_BSE',
        featureType: 'FRESH_PURCHASE',
        totalAmount: selectedAmount,
      });

      const result = await generateOTP(body);

      if (result?.meta?.code !== 'PM_TRXN_SC_112') {
        throw new Error('Failed to initiate OTP process.');
      } else {
        const otpUuid = result?.data?.uuid;
        const transactionData = {
          purchaseSchemeDetails: [
            {
              amount: selectedAmount,
              investmentType: 'SIP',
              isin: fund.isin,
              pmAmcCode: fund.pmAmcCode,
              rtaCode: fund.rtaCode,
            },
          ],
          userType: 'PML_ON_BSE',
          featureType: 'FRESH_PURCHASE',
          totalAmount: selectedAmount,
          otpDetails: {
            uuid: otpUuid,
            otp: null,
          },
          twofaAuthDetails: result?.data?.twofaAuthDetails,
        };

        showSnackbar(
          OTP_ERROR_MESSAGES.OTP_SENT_SUCCESS,
          APPEARANCE_TYPES.SUCCESS,
        );
        setOtpTransactionDetials(transactionData);
        setOpenOtpPopup(true);
        stack.pop();
        pushStack(onOTPPopupClose);
      }
    } catch (err) {
      showSnackbar(OTP_ERROR_MESSAGES.DEFAULT);
    } finally {
      setIsProcessing(false);
    }
  };

  const generatePGDeeplink = (otpRes) => {
    const emptyPspApp = { processName: '', source: '', appName: '' };
    const { paymentsTxnId, transactionId } = otpRes;

    const state = {
      selectedBank: paymentMethod,
      selectedPspApp:
        paymentMethod?.upiDetails.collect?.lastUsedVpa && isPaytmMoney()
          ? emptyPspApp
          : selectedPspApp,
      selectedVpa:
        isPaytmMoney() && paymentMethod?.upiDetails.collect?.lastUsedVpa
          ? paymentMethod?.upiDetails.collect?.lastUsedVpa
          : '',
      eventLabel3,
      eventLabel4,
      amount: selectedAmount,
      paymentsTransactionId: paymentsTxnId,
      transactionId,
      isMandateRegistrationFlow,
    };

    const encodedState = encodeURI(JSON.stringify(state));
    const base64 = window.btoa(encodedState);
    const stateDataStr = `&stateData=${base64}`;

    if (isPaytmMoney()) {
      const pmlDeeplink = `${mfH5DeeplinkPML}pg-redirection${stateDataStr}${getIsDailySipMF() ? `&isDailySipMF=${getIsDailySipMF()}` : ''}`;
      openDeepLinkPaytmMoney(pmlDeeplink);
      exitApp();
    } else {
      const { aid, sparams, params } = mfH5Deeplink;

      const deeplinkParam = `${params}&stateData=${encodedState}${getIsDailySipMF() ? `&isDailySipMF=${getIsDailySipMF()}` : ''}`;
      const deeplinkPath = 'pg-redirection';
      const deepLinkData = {
        params: deeplinkParam,
        path: deeplinkPath,
        sparams,
      };

      const encodedData = window.btoa(JSON.stringify(deepLinkData));
      const deeplink = generatePaytmDeeplink(aid, encodedData);
      openDeepLink(deeplink);

      setTimeout(() => {
        onAmountSelectionPopupClose();
        stack.pop();
      }, 500);
    }
  };

  const generateAutoPayDeeplink = (showAddNewUpi) => {
    const { aid, params, sparams } = mfH5Deeplink;
    const { isin, pmAmcCode, rtaCode, amcLogo, schemeName } = fund || {};

    const showAddNewUpiParam = showAddNewUpi ? '&showAddNewUpi=true' : '';
    const paramsStr = `&amount=${selectedAmount}&icon=${window.encodeURI(amcLogo)}&name=${window.encodeURI(schemeName)}&isin=${isin}&pmAmcCode=${pmAmcCode}&rtaCode=${rtaCode}${showAddNewUpiParam}${getIsDailySipMF() ? `&isDailySipMF=${getIsDailySipMF()}` : ''}`;

    if (isPaytmMoney()) {
      const pmlDeeplink = `${mfH5DeeplinkPML}auto-pay${paramsStr}`;
      openDeepLinkPaytmMoney(pmlDeeplink);
      exitApp();
    } else {
      const deeplinkParam = `${params}${paramsStr}`;
      const deeplinkPath = 'auto-pay';
      const deepLinkData = {
        params: deeplinkParam,
        path: deeplinkPath,
        sparams,
      };

      const encodedData = window.btoa(JSON.stringify(deepLinkData));
      const deeplink = generatePaytmDeeplink(aid, encodedData);
      openDeepLink(deeplink);

      setTimeout(() => {
        onAmountSelectionPopupClose();
        stack.pop();
      }, 500);
    }
  };

  const redirectToAutoPay = (fromChange, otpRes, showAddNewUpi = false) => {
    if (!fromChange) {
      generatePGDeeplink(otpRes);
    } else {
      generateAutoPayDeeplink(showAddNewUpi);
    }
  };

  const renderSelectAmount = () => (
    <>
      <div className={styles.header}>
        <Icon url={fund?.amcLogo} width={32} />
        <div className={styles.titleContainer}>
          <h4 className={styles.title}>{fund?.schemeName}</h4>
          <div className={styles.insideTitle}>
            {fund?.rating ? (
              <div className={styles.insideRating}>
                <p>{fund.rating}</p>
                <p>★</p>
              </div>
            ) : (
              <span className={styles.insideRating}>N/A</span>
            )}

            <div className={styles.dot}>∙</div>
            <p className={styles.subtitle}>
              {' '}
              {fund?.category} - {fund?.subCategory}
            </p>
          </div>
        </div>
      </div>
      <div className={styles.sipAmountSection}>
        <p className={styles.sipLabel}>Daily SIP Amount</p>
        <div className={styles.amountOptions}>
          {fund?.applicableSipAmount?.map((amount, index) => (
            <div
              key={index}
              className={cx(styles.amountButton, {
                [styles.selected]: selectedAmount === amount,
              })}
              onClick={() => setSelectedAmount(amount)}
            >
              ₹{amount}
            </div>
          ))}
        </div>
      </div>

      <div className={styles.middleDivider} />
      {isbankAccountsListLoading ? (
        <div className={styles.paymentSection}>
          <Shimmer height="32.5px" />
        </div>
      ) : (
        paymentMethod && (
          <div className={styles.paymentSection}>
            <div className={styles.insidePayment}>
              {selectedPspApp?.appName ? (
                <Icon
                  url={
                    selectedPspApp?.logo ||
                    paymentMethod?.upiDetails.intent?.iconUrl
                  }
                  width={32}
                />
              ) : null}
              <div className={styles.paymentDetailsContainer}>
                {paymentMethod?.upiDetails.collect?.lastUsedVpa ||
                selectedPspApp.appName ? (
                  <p className={styles.paymentMethod}>
                    {paymentMethod?.upiDetails.collect?.lastUsedVpa ||
                      (selectedPspApp.appName.toLowerCase() === 'paytm'
                        ? capitalizeFirstLetter(
                            selectedPspApp.appName.toLowerCase(),
                          )
                        : selectedPspApp.appName)}
                  </p>
                ) : null}
                {paymentMethod && (
                  <div className={styles.paymentDetails}>
                    <Icon url={paymentMethod.bankImgSrc} width={14} />
                    <span className={styles.bankDetails}>
                      {paymentMethod.displayName} -{' '}
                      {paymentMethod.bankAccountNumber}
                    </span>
                  </div>
                )}
              </div>
            </div>
            <div
              className={styles.changeButton}
              onClick={() => {
                sendPaymentEvent(true);
                redirectToAutoPay(
                  true,
                  '',
                  !(
                    paymentMethod?.upiDetails.collect?.lastUsedVpa ||
                    selectedPspApp.appName));
              }}
            >
              {paymentMethod?.upiDetails.collect?.lastUsedVpa ||
              selectedPspApp.appName
                ? 'Change'
                : 'Add UPI ID'}
              <Icon name={ICONS_NAME.LEFT_ARROW_BLUE} width={16} />
            </div>
          </div>
        )
      )}
    </>
  );

  const onAmountPopupClose = () => {
    sendAnalyticsEventDailySIP({
      action: PULSE_STATICS.ACTION.ON_CLOSE,
    });

    document.body.classList.remove(styles.bodyFixedPosition);
    setOpenOtpPopup(false);
    onAmountSelectionPopupClose();
  };

  return (
    <Drawer
      active={showAmountSelectionPopup}
      triggerClose={onAmountPopupClose}
      showGrabber
      showCloseIcon={false}
      title={openOtpPopup ? 'Confirm Investment with OTP' : ''}
      description={
        openOtpPopup
          ? `Enter OTP sent to your registered Mobile Number ${maskMobileNumber(otpTransactionDetails?.twofaAuthDetails[0]?.mobile || '')} and registered Email ID: ${getMaskedEmail(otpTransactionDetails?.twofaAuthDetails[0]?.email || '')}`
          : ''
      }
      primaryButton={
        openOtpPopup
          ? false
          : {
              label: isFreshUser
                ? `Pay & Setup Autopay of ₹${selectedAmount}`
                : 'Proceed',
              onClick: handlePayClick,
              loading: isProcessing,
              disabled: isProcessing,
            }
      }
    >
      {!openOtpPopup ? (
        renderSelectAmount()
      ) : (
        <OtpDrawer
          isOpen={openOtpPopup}
          setIsOpen={setOpenOtpPopup}
          otpTransactionDetails={otpTransactionDetails}
          setOtpMessage={setOtpMessage}
          redirectToAutoPay={redirectToAutoPay}
          otpMessage={otpMessage}
        />
      )}
    </Drawer>
  );
};

export default MfDailySipSelectAmount;
