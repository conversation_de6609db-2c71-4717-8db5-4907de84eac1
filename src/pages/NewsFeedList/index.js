import NewsFeedDrawer from '../../components/molecules/NewsFeedDetails';
import Drawer from '../../components/molecules/Drawer/Drawer';
import styles from '../NewsFeedWidgetPage/NewsFeedWidget.scss';
import { generateQueryParamsString } from '../../utils/commonUtil';
import { isPaytmMoney } from '../../utils/coreUtil';
import { exitApp } from '../../utils/bridgeUtils';

const STORAGE_NEWS = JSON.parse(localStorage.getItem('CARD_NEWS'));
const noop = () => {};

const NewsFeedListWrapper = ({
  isDrawerOpen = true,
  selectedNews = STORAGE_NEWS,
  handleDrawerDrag,
  // handleBuyClick,
  companyPageNavigation,
  onBuySellClick,
  onCompanyClick,
  onClose,
  navigateTo,
  history,
  ...props
}) => {
  console.log('NewsFeedListWrapper :: props', { navigateTo, history, props });
  // news-feed-widget-drawer

  // if (window.location.href.includes('abcd')) {
  //   import('./index.scss').catch((err) =>
  //     console.error('Failed to load transparency styles:', err),
  //   );
  // }

  return (
    <Drawer
      active={
        isDrawerOpen || window.location.href.includes('news-feed-widget-drawer')
      }
      triggerClose={() => {
        if (isPaytmMoney()) {
          console.log('isPaytmMoney = true');
          if (window?.location?.href?.includes('all-news-feed-widget-drawer')) {
            console.log('not route drawer');
            onClose();
          } else {
            console.log('is route drawer');
            exitApp();
          }
        } else {
          console.log('isPaytmMoney = false');
          onClose();
        }
      }}
      customClass={styles.newsDrawer}
      showCloseIcon={false}
    >
      <NewsFeedDrawer
        selectedNews={selectedNews}
        companyPageNavigation={companyPageNavigation}
        generateQueryParamsString={generateQueryParamsString}
        onCompanyClick={onCompanyClick}
        onBuySellClick={onBuySellClick}
        navigateTo={navigateTo}
        history={history}
      />
    </Drawer>
  );
};

export default NewsFeedListWrapper;
